import React from 'react';
import Splash from '../Screens/LandingScreen/Splash';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Splash1 from '../Screens/LandingScreen/Splash1';
import CuurentLocation from '../Screens/LandingScreen/CuurentLocation';
import SignUp from '../Screens/LandingScreen/SignUp';
import Login from '../Screens/LandingScreen/Login';
import OtpVerification from '../Screens/LandingScreen/OtpVerification';
import Global from '../Globals/Global';

const Stack = createNativeStackNavigator();

const Index = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName={
          Global.initialRoute == null ? 'Splash' : Global.initialRoute
        }>
        <Stack.Screen
          name="Splash"
          component={Splash}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Splash1"
          component={Splash1}
          options={{headerShown: false}}
        />

        <Stack.Screen
          name="CuurentLocation"
          component={CuurentLocation}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="register"
          component={SignUp}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="Login"
          component={Login}
          options={{headerShown: false}}
        />
        <Stack.Screen
          name="otp"
          component={OtpVerification}
          options={{headerShown: false}}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default Index;
