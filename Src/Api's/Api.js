import Global from '../Globals/Global';
import {API_URL} from '../utils/urls';

export async function sendOtp(data) {
  try {
    const response = await fetch(API_URL.send_otp, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    const result = await response.json();
    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.log('sendOtp error:', error);
    throw error;
  }
}

export async function verifyOtp(data) {
  try {
    const response = await fetch(API_URL.verify_otp, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.log('sendOtp error:', error);
    throw error;
  }
}

export async function getCategories() {
  try {
    const response = await fetch(API_URL.homeCategories, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();
    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.log('sendOtp error:', error);
    throw error;
  }
}

export async function getCategoriesDetails(slug) {
  try {
    const response = await fetch(`${API_URL.categories_details}/${slug}`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getTopSellingData(slug) {
  try {
    const response = await fetch(
      `${API_URL.topselling}/${slug}?page=1&limit=6`,
      {
        method: 'GET',
        headers: {
          'Content-type': 'application/json',
        },
      },
    );

    const result = await response.json();
    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getSubCatProducts(slug) {
  try {
    const response = await fetch(
      `${API_URL.subcat_products}/${slug}?page=1&limit=6`,
      {
        method: 'GET',
        headers: {
          'Content-type': 'application/json',
        },
      },
    );

    const result = await response.json();
    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getPackages(slug) {
  try {
    const response = await fetch(`${API_URL.packages}/${slug}?page=1&limit=6`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function addToCart(data) {
  try {
    const response = await fetch(`${API_URL.addtocart}`, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    console.log('RESPONSE OF CART', result);
    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function shopCategories() {
  try {
    const response = await fetch(`${API_URL.shopCategories}`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function shopSubCategories(slug) {
  try {
    const response = await fetch(`${API_URL.shopSubCategories}${slug}`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getFilters(slug) {
  console.log('urls', `${API_URL.fillters}${slug}`);
  try {
    const response = await fetch(`${API_URL.fillters}${slug}`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();
    console.log('response body:', result);

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getAllProducts(url) {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function singleProductDetails(slug) {
  console.log('urls', `${API_URL.productDetails}${slug}`);
  try {
    const response = await fetch(`${API_URL.productDetails}${slug}`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();
    console.log('response body:', result);

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getCartItems(url) {
  // console.log('urls', url);
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });

    const result = await response.json();
    console.log('response body:', result);

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function updateQuantity(body) {
  try {
    const response = await fetch(`${API_URL.cartUpdate}`, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const result = await response.json();
    console.log('response body:', result);

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getAddresses() {
  try {
    // console.log('ADRESSES url :- ', API_URL.getAddresses);
    const response = await fetch(`${API_URL.getAddresses}`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
        Authorization: `Bearer ${Global.accesstoken}`,
      },
    });

    const result = await response.json();
    console.log('response body:', result);

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function selecteAddress(id) {
  try {
    const response = await fetch(`${API_URL.selectAddress}${id}`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
        Authorization: `Bearer ${Global.accesstoken}`,
      },
    });

    const result = await response.json();

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function placeOrder(body) {
  try {
    const response = await fetch(`${API_URL.placeOrder}`, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json',
        Authorization: `Bearer ${Global.accesstoken}`,
      },
      body: JSON.stringify(body),
    });

    const result = await response.json();
    // console.log('ressasxas', result);

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function matchLocation(body) {
  try {
    console.log('sending body -> ', JSON.stringify(body));
    console.log('url -> ', API_URL.matchLocation);

    const response = await fetch(`${API_URL.matchLocation}`, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const result = await response.json();
    // console.log('match Location response', result);

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getSlots() {
  try {
    console.log('SLOTS URL -> ', `${API_URL.bookingSlots}${Global.areaId}`);

    const response = await fetch(`${API_URL.bookingSlots}${Global.areaId}`, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    });
    const result = await response.json();
    console.log('match Location response', result);
    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function getOrders(orderType, page, limit) {
  try {
    console.log(
      'sddvc,dsn',
      `${API_URL.myOrders}?page=${page}&limit=${limit}00&orderType=${orderType}`,
    );
    const response = await fetch(
      `${API_URL.myOrders}?page=1&limit=100&orderType=${orderType}`,
      {
        method: 'GET',
        headers: {
          'Content-type': 'application/json',
          Authorization: `Bearer ${Global.accesstoken}`,
        },
      },
    );

    const result = await response.json();

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}

export async function startCall(body) {
  try {
    console.log('jxjknxkjnc', API_URL.startCall);
    console.log(body, Global.accesstoken);
    const response = await fetch(API_URL.startCall, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json',
        Authorization: `Bearer ${Global.accesstoken}`,
      },
      body: JSON.stringify(body),
    });

    const result = await response.json();

    return {
      status: response.status,
      data: result,
    };
  } catch (error) {
    console.error('ERROR', error);
    throw error;
  }
}
