import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  data: 0,
  bottomtabvalue: 0,

};

const counterSlice = createSlice({
  name: 'counter',
  initialState,
  reducers: {
    setData(state, action) {
      state.data = action.payload;
    },
    setbottomvalue(state, action) {
      state.bottomtabvalue = action.payload
    },
  },
});

export const {setData, setbottomvalue} = counterSlice.actions;

export default counterSlice.reducer;
