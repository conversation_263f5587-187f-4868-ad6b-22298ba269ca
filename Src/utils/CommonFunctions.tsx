import {SetStateAction} from 'react';
import {Alert, Animated} from 'react-native';
import Global from '../Globals/Global';
import Geolocation from '@react-native-community/geolocation';
import {matchLocation} from "../Api's/Api";
import moment from 'moment';

export function convertMinutesToHrMin(minutes: number): string {
  const hrs = Math.floor(minutes / 60);
  const mins = minutes % 60;

  const hrStr = hrs > 0 ? `${hrs}hr` : '';
  const minStr = mins > 0 ? `${mins}min` : '';

  return [hrStr, minStr].filter(Boolean).join(' ');
}

export function calculateDiscountedPrice(
  originalPrice: number,
  discountPercent: number,
): number {
  const discount = (originalPrice * discountPercent) / 100;
  return originalPrice - discount;
}

export function animateText(
  fadeAnim: Animated.Value | Animated.ValueXY,
  translateYAnim: Animated.Value | Animated.ValueXY,
  keywords: string | any[],
  setIndex: {
    (value: SetStateAction<number>): void;
    (value: SetStateAction<number>): void;
    (arg0: (prev: any) => number): void;
  },
  position: string,
) {
  position == 'after' ? null : setIndex(prev => (prev + 1) % keywords.length);

  Animated.parallel([
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }),
    Animated.timing(translateYAnim, {
      toValue: -10, // move up
      duration: 300,
      useNativeDriver: true,
    }),
  ]).start(() => {
    if (position == 'after') {
      setIndex(prev => (prev + 1) % keywords.length);
    }

    // reset position below before animating in
    translateYAnim.setValue(10);

    // Fade in + slide down
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateYAnim, {
        toValue: 0, // back to normal
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  });
}

export async function getCurrentLocation() {
  Global.gettingLocation = true;
  Geolocation.getCurrentPosition(
    async position => {
      const {latitude, longitude} = position.coords;
      console.log(position);
      getAddressFromCoords(latitude, longitude);
      console.log('Current Location:', latitude, longitude);
      let latLongObj = {
        latitude: latitude,
        longitude: longitude,
      };
      Global.currentLatLng = latLongObj;
      try {
        const result = await matchLocation(latLongObj);
        if (result.data.success) {
          Global.areaId = result.data.areaId;
        } else {
          Global.areaId = undefined;
        }
      } catch (error) {
        console.error(error);
      }
    },
    error => {
      console.warn('Location error:', error.message);
      Global.gettingLocation = false;
      Global.shortAddress = 'Unable to get location!';

      Alert.alert('Location Error', error.message, [
        {
          text: 'Cancel',
          onPress: () => {},
        },
        {
          text: 'Retry',
          onPress: () => {
            Global.gettingLocation = true;
            getCurrentLocation();
          },
        },
      ]);
    },
    {
      enableHighAccuracy: true,
      timeout: 1500,
    },
  );
}

export const getAddressFromCoords = async (lat: number, lng: number) => {
  const GOOGLE_API_KEY = 'AIzaSyAjAyM3lm5no-5MGdh3Rfw8PNQhpRn0TTY';
  console.log(
    'jcbsdmbcmdsb',
    `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${GOOGLE_API_KEY}`,
  );
  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${GOOGLE_API_KEY}`,
    );
    const json = await response.json();

    if (json.results.length > 0) {
      const address = json.results[0].formatted_address;
      (Global.shortAddress = `${json.results[0].address_components[0].long_name}`),
        console.log('dskcsdlkncklsd', address.split(',')[0]);

      Global.fullAddress = address;
      Global.gettingLocation = false;
    } else {
      console.warn('No address found!');
    }
  } catch (err) {
    console.warn('Geocoding error:', err);
  }
};

export function getOrderedWeekSlots(slots = []) {
  const allWeekdays = [
    'sunday',
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
  ];

  if (!slots.length) return [];

  const firstDayInAPI = slots[0]?.day?.toLowerCase() || 'sunday';
  const startIndex = allWeekdays.indexOf(firstDayInAPI);

  const orderedDays = [
    ...allWeekdays.slice(startIndex),
    ...allWeekdays.slice(0, startIndex),
  ];

  const today = moment();

  return orderedDays.map((day, index) => {
    const matchedDay = slots.find(d => d.day.toLowerCase() === day);

    return {
      day,
      date: today.clone().add(index, 'days').format('DD'),
      fullDate: today.clone().add(index, 'days').format('DD-MM-YYYY'),
      slots: matchedDay?.slots || [],
      active: matchedDay?.active || false,
    };
  });
}
