import React, {useEffect, useState} from 'react';
import {
  Image,
  ActivityIndicator,
  View,
  StyleSheet,
  ImageProps,
  ImageResizeMode,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {createShimmerPlaceholder} from 'react-native-shimmer-placeholder';
import {SvgXml} from 'react-native-svg';

type Props = {
  uri: string;
  svgSize?: number;
  imgSize?: number;
  tintColor?: string;
  loaderSize?: number;
  imgHeight?: number;
  imgWidth?: number;
  svgHeight?: number;
  svgWidth?: number;
  resizeMode?: ImageResizeMode;
};

const DynamicImage: React.FC<Props> = ({
  uri,
  svgSize = 35,
  tintColor,
  imgSize = 35,
  imgHeight,
  imgWidth,
  svgHeight,
  resizeMode = 'cover',
  svgWidth,
}) => {
  const [svgXml, setSvgXml] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const isSvg = uri?.toLowerCase().endsWith('.svg');
  const ShimmerPlaceHolder = createShimmerPlaceholder(LinearGradient);

  useEffect(() => {
    if (isSvg) {
      setLoading(true);
      fetch(uri)
        .then(res => res.text())
        .then(text => {
          if (tintColor) {
            text = text.replace(/fill="[^"]*"/g, `fill="${tintColor}"`);
          }
          setSvgXml(text);
        })
        .catch(err => console.error('Failed to load SVG', err))
        .finally(() => setLoading(false));
    }
  }, [uri, tintColor]);

  if (isSvg) {
    if (loading || !svgXml) {
      return (
        <ShimmerPlaceHolder
          style={{height: svgHeight ?? svgSize}}
          width={svgWidth ?? svgSize}
          LinearGradient={LinearGradient}
        />
      );
    }

    return (
      <SvgXml
        xml={svgXml}
        width={svgSize}
        height={svgSize}
        style={{alignSelf: 'center'}}
      />
    );
  }

  return (
    <Image
      source={{uri}}
      resizeMode={resizeMode}
      style={{
        width: imgWidth ?? imgSize,
        height: imgHeight ?? imgSize,
        tintColor,
        alignSelf: 'center',
      }}
    />
  );
};

const styles = StyleSheet.create({
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default DynamicImage;
