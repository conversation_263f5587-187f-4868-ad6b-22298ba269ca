import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import {createShimmerPlaceholder} from 'react-native-shimmer-placeholder';

const CustomShimmer = ({height, width}) => {
  const ShimmerPlaceHolder = createShimmerPlaceholder(LinearGradient);

  return (
    <ShimmerPlaceHolder
      style={{height: height}}
      width={width}
      LinearGradient={LinearGradient}
    />
  );
};

export default CustomShimmer;

const styles = StyleSheet.create({});
