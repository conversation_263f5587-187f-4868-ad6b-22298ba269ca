import {
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import ImagePath from '../Assets/ImagePath/ImagePath';
import designeSheet from '../Designe/designeSheet';

const PackageManage = () => {
  const DATA = [
    {
      id: '1',
      price: '₹200',
      description: 'dddderrfffrfgfff',
      title: 'All In One Salon Care',
      heading: 'Waxing',
      time: '1h 33m',
      image: ImagePath.selfcareRitual,
    },
    {
      id: '2',
      price: '₹899',
      description: 'edrffggfg',
      title: 'All In One Salon Care',
      heading: 'Waxing',
      time: '1h 33m',
      image: ImagePath.selfcareRitual2,
    },
    {
      id: '3',
      price: '₹899',
      description: 'rrfftgyhh',
      title: 'All In One Salon Care',
      heading: 'Waxing',
      time: '1h 33m',
      image: ImagePath.selfcareRitual3,
    },
    {
      id: '4',
      price: '₹899',
      description: 'swaqswdee',
      title: 'All In One Salon Care',
      heading: 'Waxing',
      time: '1h 33m',
      image: ImagePath.Prettyface,
    },
    {
      id: '5',
      price: '₹899',
      description: 'frddefrffff',
      title: 'All In One Salon Care',
      time: '1h 33m',
      heading: 'Waxing',
      image: ImagePath.Prettyface,
    },
  ];

  return (
    <View style={{flex: 1}}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={{flexDirection: 'row', gap: 20}}>
          {DATA.map((item, index) => (
            <View
              key={index}
              style={{
                marginVertical: 5,
                borderRadius: 12,
                backgroundColor: '#E6E6E6',
                marginTop: 17,
                borderTopLeftRadius: 12,
                borderTopRightRadius: 12,
              }}>
              <LinearGradient
                start={{x: 0, y: 0}}
                end={{x: 1, y: 0}}
                locations={[0.0, 1.0]}
                colors={['#DAFCDE', '#EEC6C6']}
                style={{
                  borderTopLeftRadius: 12,
                  borderTopRightRadius: 12,
                  height: 35,
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <Image
                  source={ImagePath.hp}
                  style={{height: 12.5, width: 12}}
                />
                <Text
                  style={{
                    textAlign: 'center',
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#000000',
                  }}>
                  {'  Manage Your Own Package'}
                </Text>
              </LinearGradient>

              <View style={{marginHorizontal: 10, width: 276}}>
                <View
                  style={{
                    flexDirection: 'row',
                    // justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: 17,
                  }}>
                  <Text style={styles.title}>{item.title}</Text>
                  <TouchableOpacity>
                    <Image
                      style={{
                        marginLeft: 15,
                        height: 16,
                        width: 16,
                        marginHorizontal: 5,
                      }}
                      source={ImagePath.editIcon}
                    />
                  </TouchableOpacity>
                </View>

                <Text style={styles.description}>
                  <Text
                    style={{
                      fontSize: 13,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                    }}>
                    {item.heading}:{' '}
                  </Text>
                  Full Arms, Full Legs & Underarms (Rica)
                </Text>
                <Text style={styles.description}>
                  <Text
                    style={{
                      fontSize: 13,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                    }}>
                    Premium Facial:{' '}
                  </Text>
                  Korean Glow Facial ( Korean Glow Facial )
                </Text>
                <Text style={styles.description}>
                  <Text
                    style={{
                      fontSize: 13,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                    }}>
                    Manicure & Pedicure:{' '}
                  </Text>
                  Mani-Pedi Combo ( Ice Cream Mani & Pedi )
                </Text>
                <Text style={styles.description}>
                  <Text
                    style={{
                      fontSize: 13,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                    }}>
                    Facial Hair Remove:{' '}
                  </Text>
                  Eyebrows ( Threading )
                </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: 10,
                    marginBottom: 20,
                  }}>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Image
                      source={ImagePath.perimg}
                      style={{height: 14, width: 14}}
                    />
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#05945B',
                      }}>
                      {'  54% OFF'}
                    </Text>

                    <Text style={styles.price}>
                      {'  ' + item.price}

                      <Text style={styles.time}> | {item.time}</Text>
                    </Text>
                  </View>
                  <TouchableOpacity style={styles.addButton}>
                    <Text
                      style={{
                        color: '#E92E89',
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandSemiBold,
                      }}>
                      {'ADD'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default PackageManage;

const styles = StyleSheet.create({
  card: {},
  image: {
    height: 115,
    width: 113,
  },
  title: {
    fontFamily: designeSheet.QuicksandSemiBold,
    fontSize: 18,
    color: '#000000',
    flex: 1,
  },
  description: {
    color: '#6C6C6C',
    marginTop: 10,
    fontFamily: designeSheet.QuicksandRegular,
    fontSize: 12,
  },
  time: {
    color: '#888',
    marginTop: 2,
  },
  price: {
    marginTop: 2,
    fontFamily: designeSheet.QuicksandMedium,
    color: '#000',
    fontSize: 14,
  },
  addButton: {
    borderRadius: 5.12,
    borderWidth: 0.5,
    borderColor: '#E92E89',
    paddingHorizontal: 11.53,
    paddingVertical: 5.12,
    backgroundColor: '#FFF7FB',
  },
});
