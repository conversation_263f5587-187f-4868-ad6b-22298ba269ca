import React from 'react';
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import ImagePath from '../Assets/ImagePath/ImagePath';
import designeSheet from '../Designe/designeSheet';

const WaxingFilter = () => {
  const DATA = [
    {
      id: '1',
      price: 200,
      description: 'dddderrfffrfgfff',
      title: 'Waxology - Honey Aloe-Vera',
      time: '1h 33m',
      image: ImagePath.selfcareRitual,
    },
    {
      id: '2',
      price: 300,
      description: 'edrffggfg',
      title: 'Waxology - Honey Aloe-Vera',
      time: '1h 33m',
      image: ImagePath.selfcareRitual2,
    },
    {
      id: '3',
      price: 400,
      description: 'rrfftgyhh',
      title: 'Waxology - Honey Aloe-Vera',
      time: '1h 33m',
      image: ImagePath.selfcareRitual3,
    },
    {
      id: '4',
      price: 500,
      description: 'swaqswdee',
      title: 'Waxology - Honey Aloe-Vera',
      time: '1h 33m',
      image: ImagePath.Prettyface,
    },
    {
      id: '5',
      price: 400,
      description: 'frddefrffff',
      title: 'Waxology - Honey Aloe-Vera',
      time: '1h 33m',
      image: ImagePath.Prettyface,
    },
  ];

  const renderItems = ({item}) => {
    return (
      <View style={styles.card}>
        <Image source={item.image} style={styles.image} />
        <View style={styles.textContainer}>
          <TouchableOpacity style={styles.addButton}>
            <Text
              style={{
                color: '#E92E89',
                fontSize: 12,
                fontFamily: designeSheet.QuicksandSemiBold,
                textAlign: 'center',
                marginVertical: 5,
                marginHorizontal: 15,
              }}>
              Add To Cart
            </Text>
          </TouchableOpacity>
          <Text numberOfLines={2} style={styles.title}>
            {item.title}
          </Text>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Image style={{height: 11, width: 9}} source={ImagePath.rupee} />
            <Text style={styles.price}>{item.price} </Text>
            <Text style={styles.time}>| {item.time}</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={{flex: 1}}>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={DATA}
        renderItem={renderItems}
        keyExtractor={item => item.id}
      />
    </View>
  );
};

export default WaxingFilter;

const styles = StyleSheet.create({
  card: {
    padding: 10,
    marginVertical: 5,
    marginHorizontal: 5,
    height: 215,
    width: 123,
  },
  image: {
    height: 115,
    width: 113,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontFamily: designeSheet.QuicksandMedium,
    fontSize: 12,
    color: '#000000',
  },
  description: {
    color: '#6C6C6C',
    marginTop: 2,
    fontFamily: designeSheet.QuicksandRegular,
    fontSize: '12',
  },
  time: {
    color: '#888',
    marginTop: 2,
  },
  price: {
    marginTop: 2,
    fontFamily: designeSheet.QuicksandMedium,
    color: '#000',
    fontSize: 14,
  },
  addButton: {
    borderRadius: 8,
    borderWidth: 0.5,
    borderColor: '#E92E89',
    backgroundColor: '#fff',
    marginTop: -10,
    alignSelf: 'center',
    marginLeft: 5,
  },
});
