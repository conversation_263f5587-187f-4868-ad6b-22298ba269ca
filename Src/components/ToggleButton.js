import React, {useEffect, useRef, useState} from 'react';
import {View, Animated, Easing, Pressable} from 'react-native';

export default function ToggleButton({initialValue, onChange}) {
  const [toggleValue, setToggleValue] = useState(initialValue);
  const moveAnimation = useRef(new Animated.Value(0)).current;

  const WIDTH = 60;
  const HEIGHT = 30;
  const CIRCLE_SIZE = 26;
  const PADDING = 2;

  useEffect(() => {
    if (initialValue) animation();
  }, []);

  useEffect(() => {
    setToggleValue(initialValue);
    if (initialValue) {
      animation();
    } else {
      returnAnimation();
    }
  }, [initialValue]);

  function animation() {
    Animated.timing(moveAnimation, {
      toValue: WIDTH - CIRCLE_SIZE - PADDING * 2,
      duration: 500,
      easing: Easing.inOut(Easing.ease),
      useNativeDriver: true,
    }).start();
  }

  function returnAnimation() {
    Animated.timing(moveAnimation, {
      toValue: 0,
      duration: 500,
      easing: Easing.inOut(Easing.ease),
      useNativeDriver: true,
    }).start();
  }

  function toggleStatus() {
    const newValue = !toggleValue;
    setToggleValue(newValue);
    onChange?.(newValue);

    if (newValue) {
      animation();
    } else {
      returnAnimation();
    }
  }

  return (
    <View style={{paddingHorizontal: 1}}>
      <Pressable
        onPress={toggleStatus}
        style={{
          height: HEIGHT,
          width: WIDTH,
          backgroundColor: toggleValue ? '#000000' : 'gray',
          borderRadius: HEIGHT / 2,
          padding: PADDING,
          justifyContent: 'center',
        }}>
        <Animated.View
          style={{
            height: CIRCLE_SIZE,
            width: CIRCLE_SIZE,
            backgroundColor: 'white',
            borderRadius: CIRCLE_SIZE / 2,
            transform: [{translateX: moveAnimation}],
          }}
        />
      </Pressable>
    </View>
  );
}
