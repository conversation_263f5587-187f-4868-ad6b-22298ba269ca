import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import designeSheet from '../Designe/designeSheet';

type EmptyListContentProps = {
  image?: string;
  text: string;
};
const EmptyListContent: React.FC<EmptyListContentProps> = ({image, text}) => {
  return (
    <View
      style={{
        height: 400,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
      }}>
      <Image
        source={{
          uri: image,
        }}
        style={{
          height: 150,
          width: 150,
          opacity: 0.4,
        }}
      />
      <Text
        style={{
          fontSize: 17,
          fontFamily: designeSheet.QuicksandMedium,
          color: '#6C6C6C',
          opacity: 0.4,
        }}>
        {text}
      </Text>
    </View>
  );
};

export default EmptyListContent;

const styles = StyleSheet.create({});
