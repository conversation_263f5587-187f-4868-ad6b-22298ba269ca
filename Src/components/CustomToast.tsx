import {
  View,
  Text,
  StyleSheet,
  Animated,
  Image,
  Dimensions,
} from 'react-native';
import React, {Dispatch, SetStateAction} from 'react';
import ImagePath from '../Assets/ImagePath/ImagePath';
import designeSheet from '../Designe/designeSheet';

interface ToastProps {
  message: string;
  setToast: Dispatch<SetStateAction<boolean>>;
}

const CustomToast: React.FC<ToastProps> = ({message, setToast}) => {
  const bottom = React.useRef(new Animated.Value(0)).current;
  const opacity = React.useRef(new Animated.Value(1)).current;

  function animate() {
    Animated.timing(bottom, {
      toValue: 50,
      duration: 800,
      useNativeDriver: false,
    }).start(() => {
      Animated.timing(opacity, {
        toValue: 0,
        duration: 2500,
        useNativeDriver: false,
      }).start(() => {
        setToast(false);
      });
    });
  }

  React.useEffect(() => {
    animate();
  }, []);
  return (
    <Animated.View style={[styles.container, {bottom, opacity}]}>
      <View
        style={{
          alignSelf: 'center',
          alignItems: 'center',
          flexDirection: 'row',
          gap: 10,
        }}>
        <Image
          source={ImagePath.homefill}
          style={{height: 24, width: 24, resizeMode: 'contain', flex: 0}}
        />
        {/* <View style={{ flex: 1 }}> */}
        <Text
          numberOfLines={2}
          style={{
            color: 'white',
            fontSize: 14,
            fontFamily: designeSheet.QuicksandMedium,
          }}>
          {message}
        </Text>
        {/* </View> */}
      </View>
    </Animated.View>
  );
};

export default CustomToast;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    backgroundColor: 'black',
    position: 'absolute',
    alignSelf: 'center',
    borderRadius: 15,
    zIndex: 10,
    maxWidth: Dimensions.get('screen').width / 1.2,
  },
});
