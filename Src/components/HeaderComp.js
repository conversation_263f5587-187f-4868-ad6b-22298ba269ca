import {
  Image,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import ImagePath from '../Assets/ImagePath/ImagePath';
import designeSheet from '../Designe/designeSheet';

const HeaderComp = ({title, carttrue, onpressback}) => {
  return (
    <View
      style={{
        backgroundColor: '#FFF7FB',
        marginTop: Platform.OS == 'android' ? StatusBar.currentHeight + 10 : 50,
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginHorizontal: 20,
        }}>
        <TouchableOpacity onPress={onpressback} style={{}}>
          <Image
            source={ImagePath.headerarrow}
            style={{height: 13, width: 20}}
          />
        </TouchableOpacity>
        <View style={{flex: 1, alignItems: 'center'}}>
          <Text
            numberOfLines={2}
            style={{
              fontSize: 13,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              textAlign: 'center',
              width: '80%',
            }}>
            {title}
          </Text>
        </View>
        {carttrue ? (
          <Image source={ImagePath.cartimg} style={{height: 20, width: 20}} />
        ) : null}
      </View>
    </View>
  );
};

export default HeaderComp;

const styles = StyleSheet.create({});
