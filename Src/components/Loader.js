import {View, Text, ActivityIndicator} from 'react-native';
import React from 'react';

const Loader = ({isActive}) => {
  return isActive ? (
    <View
      style={{
        height: '100%',
        width: '100%',
        backgroundColor: 'rgba(255, 255,255,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        zIndex: 10,
      }}>
      <ActivityIndicator size={'large'} color={'black'} />
    </View>
  ) : null;
};

export default Loader;
