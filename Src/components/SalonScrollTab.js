import React from 'react';
import { FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ImagePath from '../Assets/ImagePath/ImagePath';
import designeSheet from '../Designe/designeSheet';

const SalonScrollTab = () => {
    const DATA = [
        {
            id: '1',
            price: 200,
            description: 'dddderrfffrfgfff',
            title: 'ddd',
            time: '1h 33m',
            image: ImagePath.Prettyface,
        },
        {
            id: '2',
            price: 300,
            description: 'edrffggfg',
            title: 'aaaa',
            time: '1h 33m',
            image: ImagePath.Prettyface,
        },
        {
            id: '3',
            price: 400,
            description: 'rrfftgyhh',
            title: 'eeee',
            time: '1h 33m',
            image: ImagePath.Prettyface,
        },
        {
            id: '4',
            price: 500,
            description: 'swaqswdee',
            title: 'rrrr',
            time: '1h 33m',
            image: ImagePath.Prettyface,
        },
        {
            id: '5',
            price: 400,
            description: 'frddefrffff',
            title: 'ttttt',
            time: '1h 33m',
            image: ImagePath.Prettyface,
        },
    ];

    const renderItems = ({ item }) => {
        return (
            <View style={styles.card}>
                <Image source={item.image} style={styles.image} />
                <View style={styles.textContainer}>
                    <Text style={styles.title}>{item.title}</Text>
                    <Text style={styles.description}>{item.description}</Text>
                    <View style={{ flexDirection: 'row',alignItems:'center' }}>
                        <Image style={{height:11,width:6}} source={ImagePath.rupee} />
                        <Text style={styles.price}>{item.price} </Text>
                        <Text style={styles.time}>| {item.time}</Text>
                        <TouchableOpacity style={styles.addButton}>
                            <Text>Add</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        );
    };

    return (
        <View style={{ flex: 1 }}>
            <FlatList
                horizontal
                showsHorizontalScrollIndicator={false}
                data={DATA}
                renderItem={renderItems}
                keyExtractor={(item) => item.id}
            />
        </View>
    );
};

export default SalonScrollTab;

const styles = StyleSheet.create({
    card: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 12,
        borderColor: '#E6E6E6',
        borderWidth: 1,
        padding: 10,
        marginVertical: 5,
        marginHorizontal: 10,
        backgroundColor: '#FFF7FB'
    },
    image: {
        width: 65,
        height: 70,
        borderRadius: 12,
        marginRight: 10,
    },
    textContainer: {
        flex: 1,
    },
    title: {
        fontFamily: designeSheet.QuicksandSemiBold,
        fontSize: 18,
        color: "#000000",
        
    },
    description: {
        color: '#6C6C6C',
        marginTop: 2,
        fontSize:'12',
        fontFamily:'Quicksand-Bold'
    },
    time: {
        color: '#888',
        marginTop: 2,
    },
    price: {
        marginTop: 2,
        fontFamily: designeSheet.QuicksandMedium,
        color: '#000',
        fontSize: 14
    },
    addButton: {
        marginLeft: 5,
        borderRadius: 8,
        borderWidth: 0.5,
        borderColor: '#E92E89',
        paddingHorizontal: 11.53,
        paddingVertical: 5.12
    }
});
