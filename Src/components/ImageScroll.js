import {
  FlatList,
  Image,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import ImagePath from '../Assets/ImagePath/ImagePath';
import designeSheet from '../Designe/designeSheet';

const ImageScroll = () => {
  const DATA = [
    {
      id: '1',
      price: 200,
      description: 'dddderrfffrfgfff',
      title: 'Ice Cream Mani - Ped',
      heading: 'Waxing',
      time: '1h 33m',
      image: ImagePath.mainiPadi,
    },
    {
      id: '2',
      price: 300,
      description: 'edrffggfg',
      title: 'Ice Cream Mani - Pedi',
      heading: 'Waxing',
      time: '1h 33m',
      image: ImagePath.maniPadiIceCream,
    },
    {
      id: '3',
      price: 400,
      description: 'rrfftgyhh',
      title: 'Ice Cream Mani - Pedi',
      heading: 'Waxing',
      time: '1h 33m',
      image: ImagePath.mainiPadi,
    },
    {
      id: '4',
      price: 500,
      description: 'swaqswdee',
      title: 'Ice Cream Mani - Pedi',
      heading: 'Waxing',
      time: '1h 33m',
      image: ImagePath.maniPadiIceCream,
    },
    {
      id: '5',
      price: 400,
      description: 'frddefrffff',
      title: 'Ice Cream Mani - Pedi',
      time: '1h 33m',
      heading: 'Waxing',
      image: ImagePath.mainiPadi,
    },
  ];

  const renderItems = ({item}) => {
    return (
      <View style={styles.card}>
        <View style={styles.textContainer}>
          <ImageBackground
            source={item.image}
            borderRadius={12}
            style={{
              width: 210,
              height: 140,
              borderRadius: 12,
              justifyContent: 'flex-end',
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 3,
              }}>
              <View style={{flex: 1}}>
                <Text style={styles.title}>{item.title}</Text>
                <Text style={styles.title}>{'899 | 1 hr 15 min'}</Text>
              </View>
              <TouchableOpacity
                style={{
                  borderRadius: 12,
                  paddingHorizontal: 5,
                  paddingVertical: 6,
                  backgroundColor: '#FFF7FB',

                  marginHorizontal: 5,
                }}>
                <Image
                  style={{height: 13, width: 14}}
                  source={ImagePath.cartIcon}
                />
              </TouchableOpacity>
            </View>
          </ImageBackground>
        </View>
      </View>
    );
  };

  return (
    <View style={{flex: 1}}>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={DATA}
        renderItem={renderItems}
        keyExtractor={item => item.id}
        contentContainerStyle={{marginHorizontal: 10}}
      />
    </View>
  );
};

export default ImageScroll;

const styles = StyleSheet.create({
  card: {
    marginVertical: 5,
  },
  image: {
    height: 115,
    width: 113,
  },
  textContainer: {
    padding: 10,
  },
  title: {
    fontFamily: designeSheet.QuicksandBold,
    fontSize: 12,
    color: '#FFFFFF',

    marginLeft: 15,
  },
  description: {
    color: '#6C6C6C',
    marginTop: 10,
    fontFamily: designeSheet.QuicksandRegular,
    fontSize: '12',
  },
  time: {
    color: '#ffffff',
    marginTop: 2,
    color: '#ffffff',
  },
  price: {
    marginTop: 2,
    fontFamily: designeSheet.QuicksandMedium,
    color: '#ffffff',
    fontSize: 11,
  },
  cartButton: {
    borderRadius: 12,
    paddingHorizontal: 5,
    paddingVertical: 6,
    backgroundColor: '#FFF7FB',
  },
});
