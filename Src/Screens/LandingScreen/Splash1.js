import React from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Image,
  TouchableOpacity,
} from 'react-native';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const { width, height } = Dimensions.get('screen');

const Onboarding = props => {
  return (
    <View style={styles.container}>

      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          props.navigation.navigate('Login');
        }}>
        <Image
          source={ImagePath.onBoarding}
          style={styles.image}
          resizeMode="stretch"
        />
      </TouchableOpacity>
    </View>
  );
};

export default Onboarding;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF7FB'
  },
  image: {
    width: width,
    height: height,
  },
});
