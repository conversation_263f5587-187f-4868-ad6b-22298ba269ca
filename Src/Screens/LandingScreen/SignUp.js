import {
  Image,
  StatusBar,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import React, { useState } from 'react';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import Loader from '../../components/Loader';
import Global from '../../Globals/Global';
import { sendOtp } from '../../Api\'s/Api';
import CustomToast from '../../components/CustomToast';

const SignUp = props => {
  const [fullName, setFullName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState(Global.phoneNumber || '');
  const [email, setEmail] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [selectGender, setSelectGender] = useState('Female');
  const [isLoading, setIsLoading] = useState(false)
  const [toast, setToast] = useState(false)
  const [message, setMessage] = useState('')

  const [errors, setErrors] = useState({});

  const validate = () => {
    const newErrors = {};
    if (!fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }
    if (!phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!/^[0-9]{10}$/.test(phoneNumber)) {
      newErrors.phoneNumber = 'Enter a valid 10-digit phone number';
    }
    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (
      !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email.trim())
    ) {
      newErrors.email = 'Invalid email address';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (validate()) {
      console.log('ckjhdsbkcbdkjb');
      let obj = {
        mobile: phoneNumber,
        type: 'register'
      };
      setIsLoading(true);
      try {
        const result = await sendOtp(obj);
        console.log('result', result);
        if (result.status == 200) {
          setMessage(result.data.message)
          setToast(true)
          setTimeout(() => {
            props.navigation.navigate(result.data.form, {
              email, phoneNumber, otp: result.data.otp, fullName, selectGender
            });
          }, 1500);
        } else {
          setMessage(result.data.message)
          setToast(true)
        }
      } catch (error) {
        console.error('Login error:', error);
      } finally {
        setTimeout(() => {
          setIsLoading(false);
        }, 1500);
      }
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#FFF7FB' }}>
      <Loader />
      <ScrollView contentContainerStyle={styles.container}>
        <StatusBar backgroundColor="#FFF7FB" barStyle={'dark-content'} />

        <Image
          source={ImagePath.splash}
          style={{ height: 67, width: 210, alignSelf: 'center', marginTop: 28 }}
        />

        <View style={{ marginTop: 38, marginHorizontal: 20 }}>
          {/* Full Name */}
          <Text style={styles.label}>Full Name</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter Full Name"
            value={fullName}
            onChangeText={setFullName}
            placeholderTextColor="gray"
          />
          {errors.fullName && <Text style={styles.error}>{errors.fullName}</Text>}

          {/* Phone Number */}
          <Text style={styles.label}>Phone Number</Text>
          <TextInput
            style={styles.input}
            placeholder="+91 8998878779"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            keyboardType="numeric"
            placeholderTextColor="gray"
            maxLength={10}
          />
          {errors.phoneNumber && <Text style={styles.error}>{errors.phoneNumber}</Text>}

          {/* Email */}
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter Your Email"
            value={email}
            onChangeText={setEmail}
            placeholderTextColor="gray"
            keyboardType="email-address"
            autoCapitalize="none"
          />
          {errors.email && <Text style={styles.error}>{errors.email}</Text>}

          {/* Referral Code */}
          <Text style={styles.label}>Referral Code</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter Referral Code"
            value={referralCode}
            onChangeText={setReferralCode}
            placeholderTextColor="gray"
          />

          {/* Gender */}
          <Text style={styles.label}>Gender</Text>
          <View style={{ flexDirection: 'row', gap: 15, marginTop: 10 }}>
            {['Female', 'Male'].map(gender => (
              <TouchableOpacity
                key={gender}
                onPress={() => setSelectGender(gender)}
                style={{
                  borderColor: selectGender === gender ? '#000' : '#CCCCCC',
                  backgroundColor: selectGender === gender ? '#000' : '#fff',
                  borderWidth: 1,
                  borderRadius: 30,
                }}>
                <Text
                  style={{
                    fontSize: 14,
                    color: selectGender === gender ? '#FFF' : '#000',
                    fontFamily: designeSheet.QuicksandMedium,
                    marginHorizontal: 20,
                    marginVertical: 10,
                  }}>
                  {gender}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <TouchableOpacity
          onPress={handleLogin}
          style={{
            backgroundColor: '#000',
            borderRadius: 100,
            marginTop: 20,
            alignItems: 'center',
            justifyContent: 'center',
            marginHorizontal: 20,
          }}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#FFFFFF',
              marginVertical: 12,
            }}>
            {'Proceed'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
      <Loader isActive={isLoading} />
      {toast && <CustomToast setToast={setToast} message={message} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFF7FB',
    marginTop: Platform.OS === 'ios' ? 80 : 40,
  },
  label: {
    fontSize: 16,
    fontFamily: designeSheet.QuicksandMedium,
    color: '#000',
    marginTop: 10,
  },
  input: {
    borderColor: '#CCCCCC',
    borderWidth: 1,
    padding: 12,
    borderRadius: 8,
    marginBottom: 5,
    fontSize: 15,
    marginTop: 10,
    fontFamily: designeSheet.QuicksandMedium,
    color: 'black',
  },
  error: {
    fontSize: 12,
    color: 'red',
    marginBottom: 5,
    marginTop: 2,
  },
});

export default SignUp;
