import {
  Image,
  StatusBar,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import React, {useState} from 'react';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import {verifyOtp} from "../../Api's/Api";
import AsyncStorage from '@react-native-async-storage/async-storage';
import Global from '../../Globals/Global';
import Loader from '../../components/Loader';
import CustomToast from '../../components/CustomToast';
import {userModel} from '../../Modals/userDataModal';
import {useDispatch} from 'react-redux';
import {setbottomvalue, setData} from '../../Redux/CreatSlice';

const OtpVerification = ({navigation, route}) => {
  const dispatch = useDispatch();
  const data = route.params;
  const [toast, setToast] = useState(false);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [value, setValue] = useState('');
  const CELL_COUNT = 4;
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });
  const ref = useBlurOnFulfill({value, cellCount: CELL_COUNT});

  const handleLogin = async () => {
    if (value.length < 4) {
      setMessage('OTP required!');
      setToast(true);
    } else if (value != data.otp) {
      setMessage('Enter a valid OTP!');
      setToast(true);
    } else {
      setIsLoading(true);

      var obj = {};
      data.email != null
        ? (obj = {
            otp: data.otp,
            mobile: data.phoneNumber,
            type: 'register',
            name: data.fullName,
            email: data.email,
            gender: data.selectGender.toLowerCase(),
          })
        : (obj = {
            otp: data.otp,
            mobile: data.phoneNumber,
            type: 'verify',
          });
      console.log('ckjhdsbkcbdkjb ', obj);

      try {
        const result = await verifyOtp(obj);
        console.log('result', result);
        if (result.status == 200) {
          AsyncStorage.setItem('user_id', result.data.data.id);
          AsyncStorage.setItem('user_data', JSON.stringify(result.data.data));
          AsyncStorage.setItem('access_token', result.data.accessToken);
          Global.user_id = result.data.data.id;
          Global.accesstoken = result.data.accessToken;
          const user = userModel(result.data.data);
          Global.userData = user;
          console.log('user - > ', user);
          setMessage(result.data.message);
          setToast(true);
          AsyncStorage.setItem('isGuest', 'false');
          Global.isGuestUser = 'false';
          setTimeout(() => {
            dispatch(setData('1'));
            dispatch(setbottomvalue(0));
          }, 1500);
        } else {
          setMessage(result.data.message);
          setToast(true);
        }
      } catch (error) {
        console.error('Login error:', error);
      } finally {
        setTimeout(() => {
          setIsLoading(false);
        }, 1500);
      }
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <ScrollView contentContainerStyle={styles.container}>
        <StatusBar backgroundColor="#FFF7FB" barStyle={'dark-content'} />

        <Image
          source={ImagePath.splash}
          style={{height: 67, width: 210, alignSelf: 'center'}}
        />

        <Text
          style={{
            fontSize: 28,
            color: '#303030',
            fontFamily: designeSheet.QuicksandBold,
            alignSelf: 'center',
            marginTop: 28,
          }}>
          {'OTP Verification'}
        </Text>
        <Text
          style={{
            fontSize: 14,
            color: '#5E5E5E',
            fontFamily: designeSheet.QuicksandMedium,
            alignSelf: 'center',
            marginTop: 10,
          }}>
          {'Enter the OPT send to  +91 9887797899'}
        </Text>

        <View style={{marginHorizontal: 30}}>
          <CodeField
            ref={ref}
            {...props}
            value={value}
            onChangeText={setValue}
            cellCount={CELL_COUNT}
            keyboardType="number-pad"
            textContentType="oneTimeCode"
            testID="my-code-input"
            renderCell={({index, symbol, isFocused}) => (
              <View
                style={{
                  borderBottomWidth: 1,
                  borderColor: 'grey',
                  flex: 1,
                  height: 65,
                  marginHorizontal: 10,
                  alignItems: 'center',
                  justifyContent: 'center',
                  paddingVertical: 20,

                  marginBottom: 10,
                }}
                key={index}
                onLayout={getCellOnLayoutHandler(index)}>
                <Text
                  style={{
                    fontSize: 20,
                    fontFamily: designeSheet.QuicksandBold,
                    color: designeSheet.black,
                  }}>
                  {symbol || (isFocused ? <Cursor /> : null)}
                </Text>
              </View>
            )}
          />
        </View>

        <TouchableOpacity style={styles.button} onPress={handleLogin}>
          <Text style={styles.buttonText}>{'Verify'}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={{flexDirection: 'row', marginHorizontal: 20}}>
          <View style={{flex: 1}}>
            <Text
              style={{
                marginTop: 10,
                fontSize: 14,
                fontFamily: designeSheet.QuicksandBold,
                color: '#6C7278',
              }}>
              {'Didn’t you receive the OTP?'}
            </Text>
          </View>
          <Text style={styles.switchText}>{'Resend OTP'}</Text>
        </TouchableOpacity>
        <Text
          style={{
            marginTop: 2,
            fontSize: 14,
            fontFamily: designeSheet.QuicksandBold,
            color: '#6C7278',
            marginLeft: 20,
          }}>
          {`OTP :-  ${data.otp}`}
        </Text>
      </ScrollView>
      <Loader isActive={isLoading} />
      {toast && <CustomToast setToast={setToast} message={message} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFF7FB',
    marginTop: Platform.OS == 'ios' ? 80 : 40,
  },
  title: {
    fontSize: 28,
    fontFamily: designeSheet.QuicksandSemiBold,
    textAlign: 'center',
  },
  logo: {
    fontSize: 24,
    textAlign: 'center',
    color: '#007b8a',
    fontFamily: designeSheet.QuicksandBold,
    marginBottom: 20,
  },
  input: {
    borderColor: '#CCCCCC',
    borderBottomWidth: 1,
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    fontSize: 15,
    marginTop: 2,
    fontFamily: designeSheet.QuicksandMedium,
  },
  button: {
    backgroundColor: '#000000',
    padding: 10,
    borderRadius: 100,
    marginTop: 10,
    marginHorizontal: 20,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 18,
    fontFamily: designeSheet.QuicksandSemiBold,
  },
  switchText: {
    marginTop: 10,
    fontSize: 14,
    fontFamily: designeSheet.QuicksandBold,
    color: 'black',
  },
  link: {
    color: '#218996',
    fontSize: 12,
    fontFamily: designeSheet.QuicksandMedium,
    textDecorationLine: 'underline',
  },
  orText: {
    textAlign: 'center',
    marginVertical: 12,
    color: '#1C1C28',
    fontSize: 12,
    fontFamily: designeSheet.QuicksandRegular,
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginTop: 15,
  },
  icon: {width: 40, height: 40, resizeMode: 'contain'},
});

export default OtpVerification;
