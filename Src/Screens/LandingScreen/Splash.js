import {
  Alert,
  Animated,
  Image,
  PermissionsAndroid,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Global from '../../Globals/Global';
import {userModel} from '../../Modals/userDataModal';
import {useDispatch} from 'react-redux';
import {setData} from '../../Redux/CreatSlice';
import {getCurrentLocation} from '../../utils/CommonFunctions';

const Splash = props => {
  const [progress] = useState(new Animated.Value(0));
  const dispatch = useDispatch();

  async function getasynchValue() {
    try {
      let userId = await AsyncStorage.getItem('user_id');
      let userdata = await AsyncStorage.getItem('user_data');
      let userToken = await AsyncStorage.getItem('access_token');
      let isGuest = await AsyncStorage.getItem('isGuest');
      let guest_id = await AsyncStorage.getItem('guest_id');
      Global.guest_id = guest_id;
      console.log('guest_id', guest_id);

      if (userdata) {
        const parseData = JSON.parse(userdata);
        const user = userModel(parseData);

        Global.user_id = userId;
        Global.userData = user;
        Global.accesstoken = userToken;

        if (userId != null) {
          dispatch(setData('1'));
        } else {
          dispatch(setData('0'));
          props.navigation.replace('Splash1');
        }
      } else if (isGuest == 'true') {
        Global.isGuestUser = isGuest;
        dispatch(setData('1'));
      } else {
        dispatch(setData('0'));
        props.navigation.replace('Splash1');
      }
    } catch (error) {
      console.error('AsyncStorage Error:', error);
    }
  }

  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'This app needs access to your location.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Location permission granted');
          getCurrentLocation(); // ✅ call here
        } else {
          console.log('Location permission denied');
          Alert.alert(
            'Permission Denied',
            'Location permission is required for app functionality.',
          );
        }
      } else {
        getCurrentLocation();
      }
    } catch (err) {
      console.warn('Permission error:', err);
    }
  };

  useEffect(() => {
    requestLocationPermission();
    Animated.timing(progress, {
      toValue: 1,
      duration: 2000,
      useNativeDriver: false,
    }).start(() => {
      getasynchValue();
    });
  });

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <StatusBar backgroundColor="white" barStyle={'dark-content'} />
      <View style={{flex: 1, marginTop: 50}}>
        <Image
          source={ImagePath.splash}
          style={{
            height: 88,
            width: 273,
            alignSelf: 'center',
          }}
        />
      </View>

      <Image
        source={ImagePath.splashimg}
        style={{
          height: 561,
          width: 323,
          alignSelf: 'center',
        }}
      />
    </View>
  );
};

export default Splash;

const styles = StyleSheet.create({});
