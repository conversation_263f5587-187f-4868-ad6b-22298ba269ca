import {
  Image,
  StatusBar,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Platform,
  Alert,
  ToastAndroid,
} from 'react-native';
import React, {useState} from 'react';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import {sendOtp} from "../../Api's/Api";
import Loader from '../../components/Loader';
import Global from '../../Globals/Global';
import CustomToast from '../../components/CustomToast';
import {setbottomvalue, setData} from '../../Redux/CreatSlice';
import {useDispatch} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Login = props => {
  const dispatch = useDispatch();

  const [number, setNumber] = useState('');
  const [per, setPer] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState(false);
  const [message, setMessage] = useState('');

  const handleLogin = async () => {
    let obj = {
      mobile: number,
      type: 'verify',
    };
    setIsLoading(true);
    try {
      const result = await sendOtp(obj);
      console.log('result', result.status);
      if (result.status == 200) {
        setMessage('Success');
        setToast(true);
        setTimeout(() => {
          props.navigation.navigate(result.data.form, {
            otp: result.data.otp,
            email: null,
            phoneNumber: number,
          });
        }, 1000);
      } else {
        setMessage(result.data.message);
        setToast(true);
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <ScrollView contentContainerStyle={styles.container}>
        <StatusBar backgroundColor="#FFF7FB" barStyle={'dark-content'} />

        <Image
          source={ImagePath.splash}
          style={{height: 67, width: 210, alignSelf: 'center'}}
        />

        <Text
          style={{
            fontSize: 28,
            color: '#303030',
            fontFamily: designeSheet.QuicksandBold,
            alignSelf: 'center',
            marginTop: 28,
          }}>
          {'Sign Up'}
        </Text>
        <Text
          style={{
            fontSize: 14,
            color: '#5E5E5E',
            fontFamily: designeSheet.QuicksandMedium,
            alignSelf: 'center',
            marginTop: 10,
          }}>
          {'Login or sign up using your mobile number.'}
        </Text>
        <View
          style={{
            borderColor: '#CCCCCC',
            borderBottomWidth: 1,
            marginTop: 30,
            marginHorizontal: 20,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              alignSelf: 'center',
            }}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#000000',
              }}>
              {'+91'}
            </Text>
            <Image
              source={ImagePath.homeimg4}
              style={{height: 12, width: 12, tintColor: '#6C7278'}}
            />
            <TextInput
              placeholder="Enter Your Mobile Number"
              placeholderTextColor={'#888888'}
              value={number}
              onChangeText={val => {
                setNumber(val);
                Global.phoneNumber = val;
              }}
              keyboardType="numeric"
              style={{color: 'black'}}
              maxLength={10}
            />
          </View>
        </View>

        <TouchableOpacity
          style={styles.button}
          onPress={() => {
            if (number.length < 10) {
              setMessage('Enter a valid number');
              setToast(true);
            } else {
              handleLogin();
            }
          }}>
          <Text style={styles.buttonText}>Get OTP</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            if (Global.guest_id == null) {
              const guestId = Math.floor(100000 + Math.random() * 900000);
              Global.guest_id = guestId;
              AsyncStorage.setItem('guest_id', guestId.toString());
              console.log(guestId);
            }
            AsyncStorage.setItem('isGuest', 'true');
            Global.isGuestUser = 'true';
            dispatch(setData('1'));
            dispatch(setbottomvalue(0));
          }}
          style={{
            backgroundColor: '#FFF',
            borderRadius: 100,
            borderWidth: 1,
            borderColor: '#000',
            marginTop: 12,
            alignItems: 'center',
            justifyContent: 'center',
            marginHorizontal: 20,
          }}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000',
              marginVertical: 12,
            }}>
            {'Continue as Guest'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            setPer(!per);
          }}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 25,
            marginHorizontal: 20,
          }}>
          <Image
            source={per ? ImagePath.yesCheck : ImagePath.sum8}
            style={{height: 14, width: 14}}
          />
          <Text
            style={{
              fontSize: 12,
              color: '#6C7278',
              fontFamily: designeSheet.QuicksandMedium,
              marginHorizontal: 5,
            }}>
            {'By continuing, you agree to our'}
            <Text
              style={{
                fontSize: 12,
                color: '#000000',
                fontFamily: designeSheet.QuicksandMedium,
              }}>
              {' Terms & Conditions'}
              <Text
                style={{
                  fontSize: 12,
                  color: '#6C7278',
                  fontFamily: designeSheet.QuicksandMedium,
                }}>
                {'and'}
                <Text
                  style={{
                    fontSize: 12,
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandMedium,
                  }}>
                  {' Privacy Policy.'}
                </Text>
              </Text>
            </Text>
          </Text>
        </TouchableOpacity>
      </ScrollView>
      <Loader isActive={isLoading} />
      {toast && <CustomToast setToast={setToast} message={message} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFF7FB',
    marginTop: Platform.OS == 'ios' ? 80 : 40,
  },
  title: {
    fontSize: 28,
    fontFamily: designeSheet.QuicksandSemiBold,
    textAlign: 'center',
  },
  logo: {
    fontSize: 24,
    textAlign: 'center',
    color: '#007b8a',
    fontFamily: designeSheet.QuicksandBold,
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#000000',
    padding: 10,
    borderRadius: 100,
    marginTop: 25,
    marginHorizontal: 20,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 18,
    fontFamily: designeSheet.QuicksandSemiBold,
  },
  switchText: {
    marginTop: 10,
    fontSize: 14,
    fontFamily: designeSheet.QuicksandBold,
    color: 'black',
  },
  link: {
    color: '#218996',
    fontSize: 12,
    fontFamily: designeSheet.QuicksandMedium,
    textDecorationLine: 'underline',
  },
  orText: {
    textAlign: 'center',
    marginVertical: 12,
    color: '#1C1C28',
    fontSize: 12,
    fontFamily: designeSheet.QuicksandRegular,
  },
  socialContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginTop: 15,
  },
  icon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
});

export default Login;
