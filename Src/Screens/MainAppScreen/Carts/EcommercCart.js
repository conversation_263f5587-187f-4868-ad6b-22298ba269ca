import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  FlatList,
  Image,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import designeSheet from '../../../Designe/designeSheet';
import HeaderComp from '../../../components/HeaderComp';
import {API_URL, IMAGE_BASE_URL} from '../../../utils/urls';
import Global from '../../../Globals/Global';
import {
  getAddresses,
  getCartItems,
  placeOrder,
  selecteAddress,
  updateQuantity,
} from "../../../Api's/Api";
import Loader from '../../../components/Loader';
import EmptyListContent from '../../../components/EmptyListContent';
import CustomToast from '../../../components/CustomToast';
import {calculateDiscountedPrice} from '../../../utils/CommonFunctions';
import {useDispatch} from 'react-redux';
import {setData} from '../../../Redux/CreatSlice';
import SelectAddress from './Popups/SelectAddress';
import {useFocusEffect} from '@react-navigation/native';

const EcommercCart = props => {
  const dispatch = useDispatch();
  const headerHeight1 = useRef(new Animated.Value(0)).current;
  const [selectAddressPopup, setSelectAddressPopup] = useState(false);

  const [cartData, setCartData] = useState([]);
  const [isLoading, setIsLoading] = useState([]);
  const [toast, setToast] = useState(false);
  const [message, setMessage] = useState('');
  const [address, setAddress] = useState([]);

  useEffect(() => {
    getProducts();
  }, []);
  useFocusEffect(
    useCallback(() => {
      getAllAddresses();
    }, []),
  );

  async function getAllAddresses() {
    try {
      const result = await getAddresses();
      if (result.status == 200) {
        setAddress(result.data.addresses);
      }
    } catch (error) {
      console.error(error);
    }
  }

  function calculateAmount() {
    let totalAmount = 0;

    const updatedItems = cartData.map(cartItem => {
      const quantity = cartItem.quantity;

      let selectedVariation;
      if (cartItem.variation) {
        selectedVariation = cartItem.itemRef.variations.find(
          v => v._id === cartItem.variation,
        );
      } else {
        selectedVariation = cartItem.itemRef.variations[0];
      }

      const price = selectedVariation?.price || 0;
      const discount = selectedVariation?.discount || 0;

      const discountedPrice = price - (price * discount) / 100;
      const subtotal = quantity * discountedPrice;

      totalAmount += subtotal;

      return {
        ...cartItem,
        price,
        discount,
        discountedPrice,
        subtotal,
      };
    });

    console.log('🧾 Updated Cart Items:', updatedItems);
    console.log('💰 Total Amount:', totalAmount);

    return totalAmount;
  }

  async function getProducts() {
    setIsLoading(true);
    var type = Global.isGuestUser == 'true' ? 'guestId' : 'userId';
    var id = Global.isGuestUser == 'true' ? Global.guest_id : Global.user_id;
    try {
      var url = `${API_URL.getCartData}?${type}=${id}&category=e-commerce`;
      console.log(url);
      const result = await getCartItems(url);
      if (result.status == 200) {
        setCartData(result.data.items);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleQuantity = async (item, type) => {
    var body = {
      cartItemId: item._id,
      quantity: type == 'minus' ? item.quantity - 1 : item.quantity + 1,
    };
    console.log('j cjsn', body);
    try {
      const result = await updateQuantity(body);
      if (result.status == 200) {
        const updatedItems = cartData.map(cartItem => {
          if (cartItem._id === item._id) {
            let quantity = cartItem.quantity;
            if (type === 'add') {
              quantity += 1;
            } else if (type === 'minus' && quantity > 1) {
              quantity -= 1;
            }
            return {
              ...cartItem,
              quantity: quantity,
            };
          }
          return cartItem;
        });
        console.log('kjsdckjsdnjcsdjkn', updatedItems);
        setCartData(updatedItems);
        calculateAmount();
      } else {
        setMessage(result.data.error);
        setToast(true);
      }
    } catch (error) {
      console.error(error);
    }
  };

  function deleteAddress(id) {
    setIsLoading(true);
    console.log('sdjcndsn', `${API_URL.deleteAddress}${id}`);
    fetch(`${API_URL.deleteAddress}${id}`, {
      method: 'DELETE',
      headers: {
        'Content-type': 'application/json',
        Authorization: `Bearer ${Global.accesstoken}`,
      },
    })
      .then(response => response.json())
      .then(response => {
        if (response.success) {
          console.log(response);
          setMessage(response.message);
          setToast(true);
          getAllAddresses();
        } else Alert.alert('Error', 'Something went wrong!');
      })
      .catch(e => console.error(e))
      .finally(() => setIsLoading(false));
  }

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <Loader isActive={isLoading} />
      {toast ? <CustomToast message={message} setToast={setToast} /> : null}

      <ScrollView nestedScrollEnabled={true} style={{flex: 1}}>
        <HeaderComp
          title={'E-commerce Cart'}
          onpressback={() => {
            props.navigation.goBack();
          }}
        />
        <FlatList
          data={cartData}
          ListEmptyComponent={() => (
            <EmptyListContent
              image="https://cdn-icons-png.flaticon.com/128/17569/17569003.png"
              text="No Item in Cart!"
            />
          )}
          renderItem={({item, index}) => (
            <View
              style={{
                borderWidth: 1,
                borderColor: '#CCCCCC',
                borderRadius: 12,
                marginHorizontal: 15,
                marginTop: 24,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  marginHorizontal: 11,
                  marginVertical: 10,
                }}>
                <Image
                  source={{uri: `${IMAGE_BASE_URL}${item.itemRef.thumbnail}`}}
                  style={{
                    // height: 100,
                    width: 103,
                    borderRadius: 10,
                  }}
                />
                <View style={{flex: 1, marginHorizontal: 20}}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                    }}>
                    {item.itemRef.name}
                  </Text>
                  <Text
                    numberOfLines={2}
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandRegular,
                      color: '#000000',
                    }}>
                    {item.itemRef.description}
                  </Text>
                  <Text
                    style={{
                      fontSize: 13,
                      fontFamily: designeSheet.QuicksandRegular,
                      color: '#6C6C6C',
                      textDecorationLine: 'line-through',
                      marginTop: 6,
                    }}>
                    {`₹` + item.itemRef.variations[0].price * item.quantity}
                    <Text
                      style={{
                        fontSize: 16,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#000000',
                        textDecorationLine: '',
                      }}>
                      {`  ₹${
                        calculateDiscountedPrice(
                          item.itemRef.variations[0].price,
                          item.itemRef.variations[0].discount,
                        ) * item.quantity
                      }`}
                      <Text
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandRegular,
                          color: '#6C6C6C',
                        }}>
                        {'   |'}
                        <Text
                          style={{
                            fontSize: 14,
                            color: '#2CAA3B',
                            fontFamily: designeSheet.QuicksandMedium,
                          }}>
                          {`   ${item.itemRef.variations[0].discount}% off`}
                        </Text>
                      </Text>
                    </Text>
                  </Text>
                  <View
                    style={{
                      padding: 8,
                      paddingVertical: 5,
                      borderWidth: 1,
                      borderColor: 'black',
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 10,
                      alignSelf: 'flex-start',
                    }}>
                    <TouchableOpacity
                      style={{
                        height: 20,
                        width: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                      onPress={() => {
                        handleQuantity(item, 'minus');
                      }}>
                      <Image
                        source={{
                          uri: 'https://cdn-icons-png.flaticon.com/128/1828/1828901.png',
                        }}
                        style={{height: 10, width: 8}}
                      />
                    </TouchableOpacity>
                    <Text
                      style={{
                        fontFamily: designeSheet.QuicksandSemiBold,
                        fontSize: 12,
                        color: 'black',
                        lineHeight: 15,
                      }}>
                      {item.quantity}
                    </Text>
                    <TouchableOpacity
                      style={{
                        height: 20,
                        width: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                      onPress={() => handleQuantity(item, 'add')}>
                      <Image
                        source={{
                          uri: 'https://cdn-icons-png.flaticon.com/128/3524/3524388.png',
                        }}
                        style={{height: 8, width: 8}}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          )}
        />
        {cartData.length == 0 ? null : (
          <View
            style={{
              backgroundColor: 'white',
              borderRadius: 15,
              padding: 15,
              margin: 15,
            }}>
            <Text
              style={{
                fontSize: 16,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {'Select and Add Address'}
            </Text>
            <View
              style={{
                borderWidth: 1,
                borderColor: '#E6E6E6',
                marginVertical: 10,
              }}
            />
            <TouchableOpacity
              onPress={() => props.navigation.navigate('AddAddress')}
              activeOpacity={0.5}
              style={{
                flexDirection: 'row',
                gap: 5,
              }}>
              <Image
                style={{height: 20, width: 20, tintColor: '#87005F'}}
                source={{
                  uri: 'https://cdn-icons-png.flaticon.com/128/6815/6815657.png',
                }}
              />
              <Text
                style={{
                  fontSize: 14,
                  color: '#87005F',
                  fontFamily: designeSheet.QuicksandBold,
                }}>
                Add new address
              </Text>
            </TouchableOpacity>
            <View
              style={{
                borderWidth: 1,
                borderColor: '#E6E6E6',
                marginVertical: 10,
              }}
            />
            <View
              style={{
                flex: 1,
                backgroundColor: 'white',
                borderRadius: 10,
              }}>
              <FlatList
                style={{}}
                data={address}
                ListEmptyComponent={() => (
                  <EmptyListContent
                    text="No Address Found"
                    image="https://cdn-icons-png.flaticon.com/128/17524/17524128.png"
                  />
                )}
                contentContainerStyle={{
                  gap: 15,
                }}
                ItemSeparatorComponent={() => (
                  <View style={{height: 0.1, backgroundColor: 'grey'}} />
                )}
                renderItem={({item, index}) => (
                  <TouchableOpacity
                    onPress={async () => {
                      try {
                        const result = await selecteAddress(item._id);
                        if (result.status == 200) {
                          getAllAddresses();
                          // (Global.shortAddress = item.houseNo),
                          //   (Global.fullAddress = item.fullAddress);
                        }
                      } catch (error) {
                        console.error(error);
                      }
                    }}
                    style={{}}>
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 5,
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 14,
                          color: '#000000',
                          fontFamily: designeSheet.QuicksandBold,
                        }}>
                        {item.placeType}
                      </Text>
                      {item.isActive ? (
                        <View
                          style={{
                            borderRadius: 5,
                            backgroundColor: '#C7F7E3',
                            alignSelf: 'flex-start',
                            padding: 5,
                            paddingHorizontal: 10,
                          }}>
                          <Text
                            style={{
                              fontSize: 10,
                              color: '#389E77',
                              fontFamily: designeSheet.QuicksandBold,
                            }}>
                            Selected
                          </Text>
                        </View>
                      ) : null}
                      <View style={{flex: 1}} />
                      {!item.isActive ? (
                        <TouchableOpacity
                          onPress={() => deleteAddress(item._id)}>
                          <Image
                            style={{
                              height: 15,
                              width: 15,
                              marginTop: 4,
                            }}
                            source={{
                              uri: 'https://cdn-icons-png.flaticon.com/128/18214/18214589.png',
                            }}
                          />
                        </TouchableOpacity>
                      ) : null}
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'flex-start',
                        gap: 5,
                      }}>
                      <Text
                        numberOfLines={3}
                        style={{
                          fontSize: 12,
                          color: 'grey',
                          fontFamily: designeSheet.QuicksandSemiBold,
                          marginBottom: 10,
                          flex: 1,
                        }}>
                        {item.fullAddress}
                      </Text>
                    </View>
                  </TouchableOpacity>
                )}
              />
            </View>
          </View>
        )}
        {cartData.length == 0 ? null : (
          <View
            style={{
              backgroundColor: 'white',
              marginHorizontal: 15,
              borderRadius: 15,
            }}>
            <Text
              style={{
                fontSize: 16,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
                marginHorizontal: 15,
                marginTop: 10,
              }}>
              {'Payment Summary'}
            </Text>

            <View
              style={{
                borderWidth: 1,
                borderColor: '#E6E6E6',
                marginTop: 10,
                marginHorizontal: 15,
              }}
            />
            <View
              style={{
                marginHorizontal: 15,
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginVertical: 8,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {'Item total'}
              </Text>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {`₹${Math.round(calculateAmount())}`}
              </Text>
            </View>
            <View
              style={{
                marginHorizontal: 15,
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginVertical: 8,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {'Tax & fees'}
              </Text>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {'₹0'}
              </Text>
            </View>
            <View
              style={{
                borderWidth: 0.8,
                borderColor: '#E6E6E6',
                marginTop: 10,
                marginHorizontal: 15,
              }}
            />
            <View
              style={{
                marginHorizontal: 15,
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginVertical: 8,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {'Amount to pay'}
              </Text>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {`₹${Math.round(calculateAmount())}`}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
      {selectAddressPopup ? (
        <SelectAddress
          headerHeight={headerHeight1}
          setSelectAddressPopup={setSelectAddressPopup}
          props={props}
        />
      ) : null}
      <TouchableOpacity
        onPress={async () => {
          if (Global.isGuestUser == 'true') {
            Alert.alert(
              'Please login!',
              'To proceed checkout please login first!',
              [
                {text: 'Cancel', onPress: () => {}},
                {
                  text: 'Ok',
                  onPress: () => {
                    Global.initialRoute = 'Login';
                    dispatch(setData('0'));
                  },
                },
              ],
            );
          } else if (address.length == 0) {
            Alert.alert(
              'Please Add Address',
              'To proceed checkout please Add Address first!',
              [
                {text: 'Cancel', onPress: () => {}},
                {
                  text: 'Ok',
                  onPress: () => {
                    setSelectAddressPopup(true);
                    Animated.timing(headerHeight1, {
                      toValue: 600,
                      duration: 600,
                      useNativeDriver: false,
                    }).start();
                  },
                },
              ],
            );
          } else {
            setIsLoading(true);
            try {
              var body = {
                bookingDate: '',
              };
              const result = await placeOrder(body);
              if (result.data.success) {
                setMessage('Your order has been placed');
                setToast(true);
                setTimeout(() => {
                  props.navigation.popToTop();
                }, 1500);
              }
            } catch (error) {
              console.error(error);
              setMessage('Something went wrong.');
              setToast(true);
            } finally {
              setTimeout(() => {
                setIsLoading(false);
              }, 1500);
            }
          }
        }}
        style={{
          backgroundColor: '#000000',
          borderRadius: 8,
          padding: 10,
          alignItems: 'center',
          justifyContent: 'center#000000',
          marginHorizontal: 15,
          margin: 15,
        }}>
        <Text
          style={{
            fontSize: 16,
            color: '#FFFFFF',
            fontFamily: designeSheet.QuicksandMedium,
          }}>
          {`Pay ₹${calculateAmount()}`}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default EcommercCart;
