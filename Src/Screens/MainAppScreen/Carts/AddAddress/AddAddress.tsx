import {
  ActivityIndicator,
  Animated,
  Dimensions,
  Image,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import MapView from 'react-native-maps';
import Global from '../../../../Globals/Global';
import designeSheet from '../../../../Designe/designeSheet';
import LinearGradient from 'react-native-linear-gradient';
import {animateText} from '../../../../utils/CommonFunctions';
import CustomShimmer from '../../../../components/CustomShimmer';
import AddOtherDetails from '../Popups/AddOtherDetails';
import Geolocation from '@react-native-community/geolocation';

const AddAddress = props => {
  const [region, setRegion] = useState({
    latitude: Global.currentLatLng.latitude,
    longitude: Global.currentLatLng.longitude,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [shortAddress, setShortAddress] = useState('');
  const [fullAddress, setFullAddress] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isLoading2, setIsLoading2] = useState(true);

  const keywords = [
    'Place the pin at exact dilivery point.',
    'Order will be dilivered here.',
  ];
  const [index, setIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;
  const [otherDetailsPopup, setOtherDetailsPopup] = useState(false);
  const headerHeight = useRef(new Animated.Value(0)).current;
  const mapRef = useRef(null);

  const getAddressFromCoords = async (lat: number, lng: number) => {
    setIsLoading(true);
    const GOOGLE_API_KEY = 'AIzaSyAjAyM3lm5no-5MGdh3Rfw8PNQhpRn0TTY';
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${GOOGLE_API_KEY}`,
      );
      const json = await response.json();

      if (json.results.length > 0) {
        const full = json.results[0].formatted_address;
        const short = json.results[0].address_components
          ?.slice(0, 2) // e.g., street number and locality
          .map(comp => comp.long_name)
          .join(', ');

        setFullAddress(full);
        setShortAddress(short || full);
      } else {
        console.warn('No address found!');
        setShortAddress('');
        setFullAddress('');
      }
    } catch (err) {
      console.warn('Geocoding error:', err);
    } finally {
      setIsLoading(false);
      setIsLoading2(false);
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      animateText(fadeAnim, translateYAnim, keywords, setIndex, 'after');
    }, 2500);
    return () => clearInterval(interval);
  }, []);

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <View
        style={{
          position: 'absolute',
          flexDirection: 'row',
          zIndex: 2,
          width: '100%',
          top: StatusBar.currentHeight,
          padding: 20,
        }}>
        <TouchableOpacity onPress={() => props.navigation.goBack()}>
          <Image
            source={{
              uri: 'https://cdn-icons-png.flaticon.com/128/54/54476.png',
            }}
            style={{height: 30, width: 30, tintColor: '#fff'}}
          />
        </TouchableOpacity>
      </View>

      <MapView
        ref={mapRef}
        style={{flex: 1}}
        mapType="standard"
        initialRegion={region}
        onRegionChange={() => setIsLoading(true)}
        onRegionChangeComplete={newRegion => {
          console.log('NEWREGION', newRegion);
          setRegion(newRegion);
          getAddressFromCoords(newRegion.latitude, newRegion.longitude);
        }}
      />

      {/* Fixed marker at the center */}
      <View style={styles.markerFixed}>
        <Image
          source={{
            uri: 'https://cdn-icons-png.flaticon.com/128/684/684908.png',
          }}
          style={{width: 40, height: 40}}
        />
      </View>

      {/* Display selected coordinates */}
      <View
        style={{
          width: '100%',
          position: 'absolute',
          bottom: 20,
          overflow: 'hidden',
          gap: 10,
        }}>
        <TouchableOpacity
          style={styles.currentLocationBtn}
          onPress={() => {
            setIsLoading2(true);
            try {
              Geolocation.getCurrentPosition(
                position => {
                  const {latitude, longitude} = position.coords;
                  const newRegion = {
                    latitude,
                    longitude,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  };
                  console.log('sdcjbsdjbcjdsb');
                  mapRef.current.animateToRegion(newRegion, 900);

                  getAddressFromCoords(latitude, longitude);
                },
                error => console.warn(error.message),
                {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000},
              );
            } catch (error) {
              console.error(error);
            }
          }}>
          <LinearGradient
            colors={['#F8F8FF', '#DCDCDC']}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 1}}
            style={styles.currentLocationGradient}>
            {isLoading2 ? (
              <ActivityIndicator size={20} color={'black'} />
            ) : (
              <Image
                source={{
                  uri: 'https://cdn-icons-png.flaticon.com/128/684/684908.png', // You can use a GPS icon instead
                }}
                style={{width: 20, height: 20, tintColor: '#000'}}
              />
            )}
            <Text
              style={{color: 'black', fontFamily: designeSheet.QuicksandBold}}>
              Current Location
            </Text>
          </LinearGradient>
        </TouchableOpacity>
        <View
          style={{
            width: '90%',
            backgroundColor: 'white',
            alignSelf: 'center',
            paddingBottom: 10,
            borderRadius: 20,
            overflow: 'hidden',
          }}>
          <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}
            locations={[0.0, 0.9]}
            colors={['#F8F8FF', '#DCDCDC']}
            style={{padding: 3, backgroundColor: 'black'}}>
            <Animated.Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                color: 'black',
                marginLeft: 10,
                opacity: fadeAnim,
                transform: [{translateY: translateYAnim}],
              }}>
              {keywords[index]}
            </Animated.Text>
          </LinearGradient>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 5,
              paddingHorizontal: 10,
            }}>
            <Image
              source={{
                uri: 'https://cdn-icons-png.flaticon.com/128/684/684908.png',
              }}
              style={{width: 20, height: 20}}
            />
            {isLoading ? (
              <View
                style={{
                  marginVertical: 5,
                  borderRadius: 6,
                  overflow: 'hidden',
                }}>
                <CustomShimmer height={20} width={100} />
              </View>
            ) : (
              <Text
                numberOfLines={1}
                style={{
                  fontSize: 17,
                  fontFamily: designeSheet.QuicksandBold,
                  color: 'black',
                  marginBottom: 5,
                  flex: 1,
                }}>
                {shortAddress}
              </Text>
            )}
          </View>
          {isLoading ? (
            <View
              style={{
                margin: 10,
                borderRadius: 7,
                overflow: 'hidden',
                alignSelf: 'flex-start',
              }}>
              <CustomShimmer
                height={28}
                width={Dimensions.get('screen').width / 1.2}
              />
            </View>
          ) : (
            <Text
              numberOfLines={2}
              style={{
                fontSize: 14,
                color: 'black',
                marginBottom: 10,
                fontFamily: designeSheet.QuicksandMedium,
                paddingHorizontal: 10,
              }}>
              {fullAddress}
            </Text>
          )}
          <TouchableOpacity
            style={styles.confirmBtn}
            onPress={() => {
              console.log('Selected Location:', region);
              console.log('Address:', fullAddress);
              setOtherDetailsPopup(true);
              Animated.timing(headerHeight, {
                toValue: 400,
                duration: 600,
                useNativeDriver: false,
              }).start();
            }}>
            <Text
              style={{color: 'white', fontFamily: designeSheet.QuicksandBold}}>
              Confirm Location
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Current Location Button */}

      {otherDetailsPopup ? (
        <AddOtherDetails
          setOtherDetailsPopup={setOtherDetailsPopup}
          headerHeight={headerHeight}
          props={props}
          fullAddress={fullAddress}
          shortAddress={shortAddress}
          region={region}
        />
      ) : null}
    </View>
  );
};

export default AddAddress;

const styles = StyleSheet.create({
  markerFixed: {
    left: Dimensions.get('window').width / 2 - 20,
    top: Dimensions.get('window').height / 2 - 40,
    position: 'absolute',
    zIndex: 10,
  },
  confirmBtn: {
    backgroundColor: '#000000',
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 10,
    marginHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullAddress: {},
  currentLocationBtn: {
    // position: 'absolute',
    // bottom: 120,
    elevation: 5,
    borderRadius: 25,
    overflow: 'hidden',
    alignSelf: 'center',
  },

  currentLocationGradient: {
    padding: 12,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 4,
  },
});
