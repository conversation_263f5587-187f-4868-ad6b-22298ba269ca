import {
  <PERSON><PERSON>,
  Animated,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {FC, useCallback, useEffect, useState} from 'react';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../../Designe/designeSheet';
import {getAddresses, selecteAddress} from "../../../../Api's/Api";
import {useFocusEffect} from '@react-navigation/native';
import Global from '../../../../Globals/Global';
import {getCurrentLocation} from '../../../../utils/CommonFunctions';
import {API_URL} from '../../../../utils/urls';
import CustomToast from '../../../../components/CustomToast';
import Loader from '../../../../components/Loader';
import EmptyListContent from '../../../../components/EmptyListContent';
type EditPopupProps = {
  setSelectAddressPopup: (value: boolean) => void;
  headerHeight: Animated.Value;
  props: any;
};
const SelectAddress: FC<EditPopupProps> = ({
  setSelectAddressPopup,
  headerHeight,
  props,
}) => {
  const [addresses, setAddresses] = useState([]);

  useFocusEffect(
    useCallback(() => {
      getAllAddresses();
    }, []),
  );
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [toast, setToast] = useState(false);

  async function getAllAddresses() {
    try {
      const result = await getAddresses();
      if (result.status == 200) {
        setAddresses(result.data.addresses);
      }
    } catch (error) {
      console.error(error);
    }
  }

  function deleteAddress(id: any) {
    setIsLoading(true);
    console.log('sdjcndsn', `${API_URL.deleteAddress}${id}`);
    fetch(`${API_URL.deleteAddress}${id}`, {
      method: 'DELETE',
      headers: {
        'Content-type': 'application/json',
        Authorization: `Bearer ${Global.accesstoken}`,
      },
    })
      .then(response => response.json())
      .then(response => {
        if (response.success) {
          console.log(response);
          setMessage(response.message);
          setToast(true);
          getAllAddresses();
        } else Alert.alert('Error', 'Something went wrong!');
      })
      .catch(e => console.error(e))
      .finally(() => setIsLoading(false));
  }

  return (
    <View
      style={{
        backgroundColor: 'rgba(0,0,0,0.5)',
        position: 'absolute',
        height: '100%',
        width: '100%',
        justifyContent: 'flex-end',
        gap: 20,
        zIndex: 4,
      }}>
      {toast ? <CustomToast message={message} setToast={setToast} /> : null}
      <Loader isActive={isLoading} />
      <TouchableOpacity
        style={{marginBottom: -10}}
        onPress={() => {
          Animated.timing(headerHeight, {
            toValue: 0,
            duration: 400,
            useNativeDriver: false,
          }).start(() => {
            setSelectAddressPopup(false);
          });
        }}>
        <Image
          source={ImagePath.crossimg}
          style={{
            height: 32,
            width: 32,
            marginTop: 20,
            marginHorizontal: 20,
            tintColor: 'white',
            alignSelf: 'flex-end',
          }}
        />
      </TouchableOpacity>
      <Animated.View
        style={{
          height: headerHeight,
          width: '100%',
          backgroundColor: '#F8F8FF',
          borderTopLeftRadius: 40,
          borderTopRightRadius: 40,
          overflow: 'hidden',
        }}>
        <ScrollView>
          <Text
            style={{
              fontSize: 18,
              color: '#000000',
              fontFamily: designeSheet.QuicksandBold,
              marginHorizontal: 20,
              marginVertical: 20,
            }}>
            {'Your Addresses'}
          </Text>
          <View
            style={{
              flex: 1,
              backgroundColor: 'white',
              marginHorizontal: 15,
              padding: 15,
              borderRadius: 10,
            }}>
            <FlatList
              style={{}}
              data={addresses}
              ListEmptyComponent={() => (
                <EmptyListContent
                  text="No Address Found"
                  image="https://cdn-icons-png.flaticon.com/128/17524/17524128.png"
                />
              )}
              contentContainerStyle={{
                gap: 15,
              }}
              ItemSeparatorComponent={() => (
                <View style={{height: 0.1, backgroundColor: 'grey'}} />
              )}
              renderItem={({item, index}) => (
                <TouchableOpacity
                  onPress={async () => {
                    try {
                      const result = await selecteAddress(item._id);
                      if (result.status == 200) {
                        getAllAddresses();
                        (Global.shortAddress = item.houseNo),
                          (Global.fullAddress = item.fullAddress);
                      }
                    } catch (error) {
                      console.error(error);
                    }
                  }}
                  style={{}}>
                  <View
                    style={{
                      flexDirection: 'row',
                      gap: 5,
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 14,
                        color: '#000000',
                        fontFamily: designeSheet.QuicksandBold,
                        marginHorizontal: 10,
                      }}>
                      {item.placeType}
                    </Text>
                    {item.isActive ? (
                      <View
                        style={{
                          borderRadius: 5,
                          backgroundColor: '#C7F7E3',
                          alignSelf: 'flex-start',
                          padding: 5,
                          paddingHorizontal: 10,
                        }}>
                        <Text
                          style={{
                            fontSize: 10,
                            color: '#389E77',
                            fontFamily: designeSheet.QuicksandBold,
                          }}>
                          Selected
                        </Text>
                      </View>
                    ) : null}
                    <View style={{flex: 1}} />
                    {item.isActive ? null : (
                      <TouchableOpacity onPress={() => deleteAddress(item._id)}>
                        <Image
                          style={{
                            height: 15,
                            width: 15,
                            marginTop: 4,
                          }}
                          source={{
                            uri: 'https://cdn-icons-png.flaticon.com/128/18214/18214589.png',
                          }}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'flex-start',
                      marginHorizontal: 10,
                      gap: 5,
                    }}>
                    <Text
                      numberOfLines={3}
                      style={{
                        fontSize: 12,
                        color: 'grey',
                        fontFamily: designeSheet.QuicksandSemiBold,
                        marginBottom: 10,
                        flex: 1,
                      }}>
                      {item.fullAddress}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
            />
          </View>
        </ScrollView>
        <TouchableOpacity
          onPress={() => props.navigation.navigate('AddAddress')}
          activeOpacity={0.5}
          style={{
            height: 48,
            borderRadius: 10,
            borderWidth: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 2,
            marginHorizontal: 20,
            marginTop: 10,
          }}>
          <Image
            style={{height: 20, width: 20}}
            source={{
              uri: 'https://cdn-icons-png.flaticon.com/128/6815/6815657.png',
            }}
          />
          <Text
            style={{
              fontSize: 14,
              color: '#000000',
              fontFamily: designeSheet.QuicksandBold,
            }}>
            Add new address
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => getCurrentLocation()}
          activeOpacity={0.5}
          style={{
            height: 48,
            borderRadius: 10,
            borderWidth: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 3,
            backgroundColor: 'black',
            marginHorizontal: 20,
            marginBottom: 15,
            marginTop: 10,
          }}>
          <Image
            style={{height: 16, width: 16, tintColor: 'white'}}
            source={{
              uri: 'https://cdn-icons-png.flaticon.com/128/484/484149.png',
            }}
          />
          <Text
            style={{
              fontSize: 14,
              color: '#ffffff',
              fontFamily: designeSheet.QuicksandBold,
              lineHeight: 20,
            }}>
            Current Address
          </Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

export default SelectAddress;

const styles = StyleSheet.create({});
