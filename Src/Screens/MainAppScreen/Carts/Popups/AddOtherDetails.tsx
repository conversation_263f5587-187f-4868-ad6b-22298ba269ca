import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Image,
  TextInput,
  Alert,
} from 'react-native';
import React, {FC, useEffect, useState} from 'react';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../../Designe/designeSheet';
import {API_URL} from '../../../../utils/urls';
import Global from '../../../../Globals/Global';
import Loader from '../../../../components/Loader';

type AddOtherDetailsProps = {
  setOtherDetailsPopup: (value: boolean) => void;
  headerHeight: Animated.Value;
  props: any;
  shortAddress: string;
  fullAddress: string;
  region: any;
};

const AddOtherDetails: FC<AddOtherDetailsProps> = ({
  setOtherDetailsPopup,
  headerHeight,
  props,
  shortAddress,
  fullAddress,
  region,
}) => {
  const [type, setType] = useState('');
  const [area, setArea] = useState('');
  const [houseNo, setHouseNo] = useState('');

  const [isDisabled, setIsDisabled] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsDisabled(type === '' || area === '' || houseNo === '');
  }, [type, houseNo, area]);

  function addAddress() {
    setIsLoading(true);
    var obj = {
      lat: region.latitude,
      lng: region.longitude,
      area: area,
      houseNo: houseNo,
      fullAddress: fullAddress,
    };

    console.log('body', obj);

    fetch(API_URL.addAddress, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json',
        Authorization: `Bearer ${Global.accesstoken}`,
      },
      body: JSON.stringify(obj),
    })
      .then(response => response.json())
      .then(response => {
        if (response.success) {
          props.navigation.goBack();
        } else Alert.alert('Error', 'Something went wrong!');
      })
      .catch(e => console.error(e))
      .finally(() => setIsLoading(false));
  }

  return (
    <View
      style={{
        backgroundColor: 'rgba(0,0,0,0.5)',
        position: 'absolute',
        height: '100%',
        width: '100%',
        justifyContent: 'flex-end',
        gap: 20,
        zIndex: 20,
      }}>
      <Loader isActive={isLoading} />
      <TouchableOpacity
        style={{marginBottom: -10}}
        onPress={() => {
          Animated.timing(headerHeight, {
            toValue: 0,
            duration: 400,
            useNativeDriver: false,
          }).start(() => {
            setOtherDetailsPopup(false);
          });
        }}>
        <Image
          source={ImagePath.crossimg}
          style={{
            height: 32,
            width: 32,
            marginTop: 20,
            marginHorizontal: 20,
            tintColor: 'white',
            alignSelf: 'flex-end',
          }}
        />
      </TouchableOpacity>
      <Animated.View
        style={{
          height: headerHeight,
          width: '100%',
          backgroundColor: '#FFF7FB',
          borderTopLeftRadius: 40,
          borderTopRightRadius: 40,
          paddingHorizontal: 20,
          paddingVertical: 10,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: 5,
          }}>
          <Image
            source={{
              uri: 'https://cdn-icons-png.flaticon.com/128/684/684908.png',
            }}
            style={{width: 20, height: 20}}
          />

          <Text
            numberOfLines={1}
            style={{
              fontSize: 17,
              fontFamily: designeSheet.QuicksandBold,
              color: 'black',
              marginBottom: 5,
              flex: 1,
            }}>
            {shortAddress}
          </Text>
        </View>
        <Text
          numberOfLines={2}
          style={{
            fontSize: 14,
            color: 'black',
            marginBottom: 10,
            fontFamily: designeSheet.QuicksandMedium,
          }}>
          {fullAddress}
        </Text>

        <View style={{gap: 5}}>
          <Text
            style={{
              fontSize: 14,
              color: 'black',
              fontFamily: designeSheet.QuicksandBold,
            }}>
            House / Flat / Block No.
          </Text>
          <TextInput
            style={{
              borderWidth: 1,
              borderColor: 'grey',
              borderRadius: 10,
              paddingHorizontal: 10,
              color: 'black',
              fontFamily: designeSheet.QuicksandSemiBold,
            }}
            placeholder="House / Flat / Block No"
            placeholderTextColor={'lightgrey'}
            onChangeText={setHouseNo}
          />
        </View>

        <View style={{gap: 5, marginTop: 15}}>
          <Text
            style={{
              fontSize: 14,
              color: 'black',
              fontFamily: designeSheet.QuicksandBold,
            }}>
            Apartment / Road / Area (RECOMMENDED)
          </Text>
          <TextInput
            style={{
              borderWidth: 1,
              borderColor: 'grey',
              borderRadius: 10,
              paddingHorizontal: 10,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: 'black',
            }}
            placeholder="Apartment / Road / Area"
            placeholderTextColor={'lightgrey'}
            onChangeText={setArea}
          />
        </View>

        <View style={{gap: 5, marginTop: 15}}>
          <Text
            style={{
              fontSize: 14,
              color: 'black',
              fontFamily: designeSheet.QuicksandBold,
            }}>
            Type of Address
          </Text>
          <View style={{flexDirection: 'row', gap: 10}}>
            {['Home', 'Office', 'Other'].map((item, index) => (
              <TouchableOpacity
                onPress={() => setType(item)}
                style={{
                  borderWidth: 1,
                  paddingHorizontal: 10,
                  paddingVertical: 5,
                  borderRadius: 5,
                  backgroundColor: item == type ? 'black' : 'transparent',
                }}>
                <Text
                  style={{
                    fontSize: 14,
                    color: item == type ? 'white' : 'black',
                    fontFamily: designeSheet.QuicksandBold,
                    lineHeight: 20,
                  }}>
                  {item}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        <View style={{flex: 1}} />
        <TouchableOpacity
          disabled={isDisabled}
          style={{
            backgroundColor: !isDisabled ? '#000000' : 'grey',
            paddingVertical: 12,
            borderRadius: 8,
            marginTop: 10,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onPress={() => addAddress()}>
          <Text
            style={{color: 'white', fontFamily: designeSheet.QuicksandBold}}>
            Add Address
          </Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

export default AddOtherDetails;
