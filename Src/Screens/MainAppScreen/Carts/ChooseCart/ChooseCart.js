import {StyleSheet, Text, View, TouchableOpacity, Image} from 'react-native';
import React from 'react';

const ChooseCart = ({navigation}) => {
  const handleCartPress = (cartType: 'ecommerce' | 'beauty') => {
    // Replace with your navigation logic
    navigation.navigate(
      cartType === 'ecommerce' ? 'EcommercCart' : 'BeautyCart',
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Choose Your Cart</Text>

      <TouchableOpacity
        style={styles.card}
        onPress={() => handleCartPress('ecommerce')}>
        {/* <Image
          source={require('../../assets/ecommerce.png')} // use your icon or image here
          style={styles.icon}
          resizeMode="contain"
        /> */}
        <Text style={styles.cardTitle}>E-Commerce Cart</Text>
        <Text style={styles.cardDesc}>
          Discover deals on clothing, beauty, and more.
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.card}
        onPress={() => handleCartPress('beauty')}>
        {/* <Image
          source={require('../../assets/beauty.png')} // use your icon or image here
          style={styles.icon}
          resizeMode="contain"
        /> */}
        <Text style={styles.cardTitle}>Beauty Cart</Text>
        <Text style={styles.cardDesc}>
          Book beauty services and explore wellness options.
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default ChooseCart;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF7FB',
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 30,
    textAlign: 'center',
    color: '#333',
  },
  card: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    marginBottom: 20,
    alignItems: 'center',
  },
  icon: {
    width: 60,
    height: 60,
    marginBottom: 15,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#222',
    marginBottom: 6,
  },
  cardDesc: {
    fontSize: 14,
    color: '#555',
    textAlign: 'center',
  },
});
