import {
  Dimensions,
  Image,
  Modal,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';
import ImageCropPicker from 'react-native-image-crop-picker';
import {Calendar} from 'react-native-calendars';
const {width} = Dimensions.get('window');

const EditProfile = props => {
  const [name, setName] = useState('');
  const [showMediaPopup, setShowMediaPopup] = useState(false);
  const [profileimage, setProfileimage] = useState('');
  const [selectGender, setSelectGender] = useState('Male');
  const [showSlotModal, setShowSlotModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const handle_cameraPressImg = () => {
    setShowMediaPopup(true);
  };
  const handle_galleryPress = () => {
    ImageCropPicker.openPicker({
      width: 300,
      height: 400,
      cropping: true,
    }).then(image => {
      console.log(image);
      setProfileimage(image.path);
      setShowMediaPopup(false);
    });
  };

  const handle_cameraPress = () => {
    ImageCropPicker.openCamera({
      width: 300,
      height: 400,
      cropping: true,
    }).then(image => {
      setProfileimage(image.path);
      setShowMediaPopup(false);
    });
  };
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Your Profile'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View style={{flex: 1}}>
        <TouchableOpacity
          onPress={() => handle_cameraPressImg()}
          style={{
            alignItems: 'center',
            marginTop: 20,
          }}>
          <Image
            source={
              profileimage == '' ? ImagePath.profilenew : {uri: profileimage}
            }
            style={{
              height: 54,
              width: 54,
              borderRadius: 50,
            }}
          />
          <Text
            style={{
              fontSize: 12,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#1B5ABB',
              marginTop: 4,
            }}>
            {'Change Profile Picture'}
          </Text>
        </TouchableOpacity>
        <View
          style={{
            marginHorizontal: 20,
            marginTop: 20,
          }}>
          <TextInput
            style={styles.input}
            placeholder="Palak"
            value={name}
            onChangeText={setName}
          />
          <TextInput
            style={styles.input}
            placeholder="Thakur"
            value={name}
            onChangeText={setName}
          />
          <TextInput
            style={styles.input}
            placeholder="<EMAIL>"
            value={name}
            onChangeText={setName}
          />

          <TextInput
            style={styles.input}
            placeholder="+91 9878798978"
            value={name}
            onChangeText={setName}
          />

          <TouchableOpacity
            onPress={() => {
              setShowSlotModal(true);
            }}
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCC',
              padding: 5,
              borderRadius: 10,
              marginBottom: 14,
              marginTop: 5,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <Text
              style={{
                marginHorizontal: 4,
                marginVertical: 10,
                color: '#6C6C6C',
              }}>
              {selectedDate ? selectedDate : '12-10-2001'}
            </Text>

            <Image
              source={ImagePath.cal}
              style={{
                width: 16,
                height: 16,
                marginHorizontal: 10,
              }}
            />
          </TouchableOpacity>
        </View>
        <View
          style={{
            flexDirection: 'row',
            marginHorizontal: 20,
            gap: 15,
          }}>
          <TouchableOpacity
            onPress={() => {
              setSelectGender('Male');
            }}
            style={{
              borderColor: selectGender == 'Male' ? '' : '#CCCCCCCC',
              backgroundColor: selectGender == 'Male' ? '#000000' : '#ffffff',

              borderWidth: 1,
              borderRadius: 4,
            }}>
            <Text
              style={{
                fontSize: 14,
                color: selectGender == 'Male' ? '#FFFFFF' : '#000000',
                fontFamily: designeSheet.QuicksandMedium,
                marginHorizontal: 20,
                marginVertical: 10,
              }}>
              {'Male'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setSelectGender('Female');
            }}
            style={{
              borderColor: selectGender == 'Female' ? '' : '#CCCCCCCC',
              backgroundColor: selectGender == 'Female' ? '#000000' : '#ffffff',
              borderRadius: 4,
              borderWidth: 1,
            }}>
            <Text
              style={{
                fontSize: 14,
                color: selectGender == 'Female' ? '#FFFFFF' : '#000000',
                fontFamily: designeSheet.QuicksandSemiBold,
                marginHorizontal: 20,
                marginVertical: 10,
              }}>
              {'Female'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <TouchableOpacity
        onPress={() => {
          props.navigation.goBack();
        }}
        style={{
          backgroundColor: '#000000',
          borderRadius: 8,
          marginBottom: 20,
          marginHorizontal: 20,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#FFFFFF',
            marginVertical: 12,
          }}>
          {'Update Profile'}
        </Text>
      </TouchableOpacity>
      {showMediaPopup == true ? (
        <View
          style={{
            position: 'absolute',
            height: '100%',
            width: '100%',
            backgroundColor: 'rgba(0,0,0,0.5)',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              backgroundColor: '#E92E89',
              borderRadius: 16,
            }}>
            <TouchableOpacity
              onPress={() => setShowMediaPopup(false)}
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                height: 25,
                width: 25,
                borderRadius: 25,
                backgroundColor: 'white',
                alignSelf: 'flex-end',
                marginHorizontal: 10,
                marginTop: 10,
              }}>
              <Image
                style={{
                  height: 10,
                  width: 10,
                  tintColor: 'black',
                }}
                source={{
                  uri: 'https://cdn-icons-png.flaticon.com/128/1828/1828778.png',
                }}
              />
            </TouchableOpacity>

            <View
              style={{
                marginVertical: 25,
                marginHorizontal: 40,
              }}>
              <TouchableOpacity
                onPress={() => handle_cameraPress()}
                activeOpacity={1}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                }}>
                <Image
                  source={ImagePath.camera}
                  style={{
                    marginHorizontal: 10,
                    height: 20,
                    width: 20,
                    tintColor: 'white',
                  }}
                />
                <Text
                  style={{
                    fontSize: 18,
                    marginHorizontal: 10,
                    fontFamily: designeSheet.PoppinsRegular,
                    color: 'white',
                  }}>
                  {'Camera'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handle_galleryPress()}
                activeOpacity={1}
                style={{
                  flexDirection: 'row',
                  marginTop: 10,
                }}>
                <Image
                  source={ImagePath.gallery}
                  style={{
                    marginHorizontal: 10,
                    height: 20,
                    width: 20,
                    tintColor: 'white',
                  }}
                />
                <Text
                  style={{
                    fontSize: 18,
                    marginHorizontal: 10,
                    fontFamily: designeSheet.PoppinsRegular,
                    color: 'white',
                  }}>
                  {'Gallery'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      ) : null}
      <Modal transparent visible={showSlotModal} animationType="slide">
        <View
          style={{
            flex: 1,
            backgroundColor: '#00000077',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              backgroundColor: '#fff',
              borderRadius: 16,
              padding: 20,
              width: width - 40,
            }}>
            <Calendar
              onDayPress={day => {
                setSelectedDate(day.dateString);
                setShowSlotModal(false);
              }}
              markedDates={{
                [selectedDate]: {
                  selected: true,
                  selectedColor: '#000',
                },
              }}
              style={{borderRadius: 12}}
              theme={{arrowColor: '#000'}}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default EditProfile;

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    padding: 14,
    borderRadius: 10,
    marginBottom: 14,
    marginTop: 5,
  },
});
