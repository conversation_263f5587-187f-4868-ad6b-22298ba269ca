import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Modal,
  FlatList,
  Dimensions,
  Alert,
} from 'react-native';
import React, {useState} from 'react';
import {Calendar} from 'react-native-calendars';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import {setbottomvalue} from '../../Redux/CreatSlice';
import {useDispatch} from 'react-redux';

const {width} = Dimensions.get('window');

const BookConsult = props => {
  // form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [number, setNumber] = useState('');
  const [concern, setConcern] = useState('Ageing');
  const [consultation, setConsultation] = useState('Chandigarh');
  const [message, setMessage] = useState('');
  const [partialPay, setPartialPay] = useState(false);
  const [agreed, setAgreed] = useState(false);
  const dispatch = useDispatch();
  // calendar + time
  const [showSlotModal, setShowSlotModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [hour, setHour] = useState('10');
  const [minute, setMinute] = useState('00');
  const [period, setPeriod] = useState('AM');
  const [timeModalVisible, setTimeModalVisible] = useState(false);
  const [timeSelectType, setTimeSelectType] = useState(null);

  const timeOptions = {
    hour: Array.from({length: 12}, (_, i) => `${i + 1}`.padStart(2, '0')),
    minute: ['00', '15', '30', '45'],
    period: ['AM', 'PM'],
  };

  const formattedSlot = () => {
    if (!selectedDate) return 'Select Date & Time';
    const dateObj = new Date(selectedDate);
    return `${dateObj.toDateString()} - ${hour}:${minute} ${period}`;
  };

  const handleBook = () => {
    if (!name || !email || !number || !selectedDate || !agreed) {
      Alert.alert('Please fill all required fields and accept terms');
      return;
    }

    Alert.alert('Booking successful!', '', [
      {
        text: 'OK',
        onPress: () => {
          dispatch(setbottomvalue(0));
          props.navigation.navigate('Home');
        },
      },
    ]);
  };

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Book Your Expert Consultation'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <SafeAreaView style={{flex: 1}}>
        <ScrollView contentContainerStyle={{padding: 16}}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#0D0D0D',
            }}>
            {'Full Name'}
          </Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your name"
            value={name}
            onChangeText={setName}
          />
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#0D0D0D',
            }}>
            {'Email'}
          </Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your mail"
            keyboardType="email-address"
            value={email}
            onChangeText={setEmail}
          />
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#0D0D0D',
            }}>
            {'Number'}
          </Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your number"
            keyboardType="phone-pad"
            value={number}
            onChangeText={setNumber}
          />
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#0D0D0D',
            }}>
            {'Concern'}
          </Text>
          {/* Concern Dropdown */}
          <TouchableOpacity style={styles.input}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#6C6C6C',
              }}>
              {concern}
            </Text>
          </TouchableOpacity>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#0D0D0D',
            }}>
            {'Consultation'}
          </Text>
          {/* Consultation Dropdown */}
          <TouchableOpacity style={styles.input}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#6C6C6C',
              }}>
              {consultation}
            </Text>
          </TouchableOpacity>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#0D0D0D',
            }}>
            {'Slot'}
          </Text>
          {/* Slot Picker */}
          <TouchableOpacity
            style={styles.input}
            onPress={() => setShowSlotModal(true)}>
            <Text
              style={{
                color: selectedDate ? '#000' : '#6C6C6C',
                fontSize: 14,
                fontFamily: designeSheet.QuicksandMedium,
              }}>
              {formattedSlot()}
            </Text>
          </TouchableOpacity>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#0D0D0D',
            }}>
            {'Amount'}
          </Text>
          {/* Amount */}
          <TextInput style={styles.input} value="₹30,000" editable={false} />
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#0D0D0D',
            }}>
            {'Briefly tell us  how we can help'}
          </Text>
          {/* Textarea */}
          <TextInput
            placeholder="Tell us about your concern..."
            value={message}
            onChangeText={setMessage}
            style={[styles.input, {height: 100, textAlignVertical: 'top'}]}
            multiline
          />

          {/* Checkboxes */}
          <TouchableOpacity
            style={styles.checkboxRow}
            onPress={() => setPartialPay(!partialPay)}>
            <View style={styles.checkbox}>
              {partialPay && <View style={styles.checked} />}
            </View>
            <Text style={styles.checkboxLabel}>
              Pay ₹6,000 now, rest pay at Consultation centre
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checkboxRow}
            onPress={() => setAgreed(!agreed)}>
            <View style={styles.checkbox}>
              {agreed && <View style={styles.checked} />}
            </View>
            <Text style={styles.checkboxLabel}>
              I agree to the <Text style={styles.link}>Terms & Conditions</Text>{' '}
              & <Text style={styles.link}>Privacy Policy</Text>
            </Text>
          </TouchableOpacity>

          {/* Button */}
          <TouchableOpacity style={styles.button} onPress={handleBook}>
            <Text style={styles.buttonText}>Book Now</Text>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>

      {/* Slot Calendar Modal */}
      <Modal transparent visible={showSlotModal} animationType="slide">
        <View style={styles.overlay}>
          <View style={styles.slotModal}>
            <Calendar
              onDayPress={day => setSelectedDate(day.dateString)}
              markedDates={{
                [selectedDate]: {
                  selected: true,
                  selectedColor: '#000',
                },
              }}
              style={{borderRadius: 12}}
              theme={{arrowColor: '#000'}}
            />

            {/* Time */}
            <View style={styles.timeRow}>
              <TouchableOpacity
                style={styles.timeBox}
                onPress={() => {
                  setTimeSelectType('hour');
                  setTimeModalVisible(true);
                }}>
                <Text style={styles.timeText}>{hour}</Text>
              </TouchableOpacity>
              <Text style={styles.colon}>:</Text>
              <TouchableOpacity
                style={styles.timeBox}
                onPress={() => {
                  setTimeSelectType('minute');
                  setTimeModalVisible(true);
                }}>
                <Text style={styles.timeText}>{minute}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.timeBox}
                onPress={() => {
                  setTimeSelectType('period');
                  setTimeModalVisible(true);
                }}>
                <Text style={styles.timeText}>{period}</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={styles.confirmBtn}
              onPress={() => setShowSlotModal(false)}>
              <Text style={styles.confirmText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Time Picker Modal */}
      <Modal transparent visible={timeModalVisible} animationType="fade">
        <TouchableOpacity
          style={styles.overlay}
          onPress={() => setTimeModalVisible(false)}
          activeOpacity={1}>
          <View style={styles.timeModal}>
            <FlatList
              data={timeOptions[timeSelectType]}
              keyExtractor={item => item}
              renderItem={({item}) => (
                <TouchableOpacity
                  onPress={() => {
                    if (timeSelectType === 'hour') setHour(item);
                    if (timeSelectType === 'minute') setMinute(item);
                    if (timeSelectType === 'period') setPeriod(item);
                    setTimeModalVisible(false);
                  }}
                  style={styles.timeItem}>
                  <Text style={styles.timeText}>{item}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

export default BookConsult;

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    padding: 14,
    borderRadius: 10,
    marginBottom: 14,
    marginTop: 5,
  },
  button: {
    backgroundColor: '#000000',
    padding: 16,
    borderRadius: 10,
    marginTop: 10,
  },
  buttonText: {
    color: '#FCFCFC',
    fontWeight: designeSheet.QuicksandSemiBold,
    textAlign: 'center',
    fontSize: 16,
  },
  checkboxRow: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checked: {
    width: 12,
    height: 12,
    backgroundColor: '#CCCCCC',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 14,
    color: '#6C6C6C',
    fontFamily: designeSheet.QuicksandSemiBold,
  },
  link: {
    textDecorationLine: 'underline',
    color: '#000000',
    fontFamily: designeSheet.QuicksandBold,
  },
  overlay: {
    flex: 1,
    backgroundColor: '#00000077',
    justifyContent: 'center',
    alignItems: 'center',
  },
  slotModal: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: width - 40,
  },
  timeRow: {
    flexDirection: 'row',
    marginTop: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeBox: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    marginHorizontal: 6,
    width: 60,
    alignItems: 'center',
  },
  colon: {
    fontSize: 20,
    fontWeight: '600',
  },
  confirmBtn: {
    backgroundColor: '#000',
    padding: 14,
    borderRadius: 8,
    marginTop: 16,
  },
  confirmText: {
    color: '#fff',
    textAlign: 'center',
    fontWeight: '600',
  },
  timeModal: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: 120,
    maxHeight: 300,
  },
  timeItem: {
    padding: 14,
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
  },
});
