import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';

const ContactUs = props => {
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Contact us'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <Image
        source={ImagePath.contactus}
        style={{
          height: 230,
          width: 335,
          alignSelf: 'center',
          marginTop: 15,
        }}
      />
      <View
        style={{
          alignItems: 'center',
          marginTop: 20,
        }}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
          }}>
          {'Need Help?'}
        </Text>
        <Text
          style={{
            fontSize: 12,
            fontFamily: designeSheet.QuicksandMedium,
            color: '#000000',
          }}>
          {'Have question or concerns? Connect with us here!'}
        </Text>
      </View>
      <View
        style={{
          backgroundColor: '#FFFFFF',
          marginHorizontal: 20,
          borderRadius: 28,
          marginTop: 15,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginVertical: 5,
            justifyContent: 'space-between',
          }}>
          <Image
            source={ImagePath.conback}
            style={{height: 42, width: 42, marginHorizontal: 5}}
          />
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandMedium,
              color: '#000000',
            }}>
            {'Call Now'}
          </Text>
          <Image
            source={ImagePath.homeimg4}
            style={{height: 14, width: 14, marginHorizontal: 5}}
          />
        </View>
      </View>
      <View style={{marginHorizontal: 20, marginTop: 15}}>
        <Text
          style={{
            fontSize: 12,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
          }}>
          {'Note:'}
          <Text
            style={{
              fontSize: 12,
              fontFamily: designeSheet.QuicksandMedium,
              color: '#6C6C6C',
            }}>
            {
              ' You can connect with our chat support via Help Centre, from 08:00 Am to 06:00 Pm '
            }
          </Text>
        </Text>
      </View>
    </View>
  );
};

export default ContactUs;

const styles = StyleSheet.create({});
