import {
  Image,
  ImageBackground,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';
import {SafeAreaView} from 'react-native-safe-area-context';
import Global from '../../Globals/Global';

const Shop = props => {
  const [search, setSearch] = useState('');
  const [cate, setCate] = useState([
    {
      image1: ImagePath.shop1,
      image2: ImagePath.shop5,
      text1: 'Dresses',
      text2: 'T-Shirt',
    },
    {
      image1: ImagePath.shop2,
      image2: ImagePath.shop6,
      text1: 'Tops',
      text2: 'Kurta',
    },
    {
      image1: ImagePath.shop3,
      image2: ImagePath.shop7,
      text1: '<PERSON><PERSON>',
      text2: 'Shirt',
    },
    {
      image1: ImagePath.shop4,
      image2: ImagePath.shop8,
      text1: 'Kurta set',
      text2: 'Jeans',
    },
  ]);
  const [topDeal, setTopDeal] = useState([
    {
      image: ImagePath.shop9,
      text: 'Women Embroidered',
    },
    {
      image: ImagePath.shop10,
      text: 'Men T-shirt',
    },
    {
      image: ImagePath.shop10,
      text: 'Men T-shirt',
    },
    {
      image: ImagePath.shop9,
      text: 'Women Embroidered',
    },
    {
      image: ImagePath.shop10,
      text: 'Men T-shirt',
    },
  ]);
  const [bestPick, setBestPick] = useState([
    {
      imageData: ImagePath.shop13,
    },
    {
      imageData: ImagePath.shop14,
    },
    {
      imageData: ImagePath.shop15,
    },
    {
      imageData: ImagePath.shop13,
    },
    {
      imageData: ImagePath.shop14,
    },
    {
      imageData: ImagePath.shop15,
    },
  ]);
  const [trend, setTrend] = useState([
    {
      image: ImagePath.shop16,
    },
    {
      image: ImagePath.shop17,
    },
    {
      image: ImagePath.shop16,
    },
    {
      image: ImagePath.shop17,
    },
    {
      image: ImagePath.shop16,
    },
    {
      image: ImagePath.shop17,
    },
  ]);
  const [trendNear, setTrendNear] = useState([
    {
      image: ImagePath.shop21,
      text: 'Women Embroidered',
    },
    {
      image: ImagePath.shop22,
      text: 'Men T-shirt',
    },
    {
      image: ImagePath.shop21,
      text: 'Men T-shirt',
    },
    {
      image: ImagePath.shop22,
      text: 'Women Embroidered',
    },
    {
      image: ImagePath.shop21,
      text: 'Men T-shirt',
    },
  ]);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <ScrollView nestedScrollEnabled={true} style={{flex: 1}}>
        <ImageBackground
          style={{
            width: '100%',
            paddingTop: Platform.OS == 'android' ? StatusBar.currentHeight : 40,
            paddingBottom: 10,
          }}
          source={ImagePath.shopback}>
          <View style={styles.addressView}>
            <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
              <Image
                style={{height: 20.41, width: 16}}
                source={ImagePath.locationimg}
              />
              <View>
                <View style={styles.addressTextView}>
                  <Text style={styles.addressMainText}>
                    {Global.shortAddress}
                  </Text>
                  <Image style={{marginLeft: 5}} source={ImagePath.ArrowDown} />
                </View>
                <Text numberOfLines={1} style={styles.addressText}>
                  {Global.fullAddress}
                </Text>
              </View>
            </View>

            <Image source={ImagePath.cartimg} style={{height: 20, width: 20}} />
          </View>
          <View
            style={{
              backgroundColor: '#FFF7FB',
              marginHorizontal: 20,
              marginTop: 10,
              borderRadius: 10,
              flexDirection: 'row',
              alignItems: 'center',
              padding: 3,
            }}>
            <Image
              source={ImagePath.searchimg}
              style={{height: 19, width: 19, marginHorizontal: 10}}
            />
            <TextInput
              placeholder="Search for “scrub”"
              placeholderTextColor={'#6C6C6C'}
              style={{
                fontSize: 14,
                color: '#6C6C6C',
                fontFamily: designeSheet.QuicksandBold,
              }}
              value={search}
              onChangeText={setSearch}
            />
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 20,
              marginTop: 20,
              gap: 20,
            }}>
            {cate.map((item, index) => (
              <TouchableOpacity
                onPress={() => {
                  props.navigation.navigate('ViewAll');
                }}
                key={index}
                style={{gap: 10}}>
                <View style={{alignItems: 'center'}}>
                  <Image source={item.image1} style={{height: 70, width: 72}} />
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#000000',
                      marginTop: 5,
                    }}>
                    {item.text1}
                  </Text>
                </View>
                <View style={{alignItems: 'center'}}>
                  <Image source={item.image2} style={{height: 70, width: 72}} />
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#000000',
                      marginTop: 5,
                    }}>
                    {item.text2}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </ImageBackground>
        <Text
          style={{
            fontSize: 18,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: 10,
          }}>
          {'Top Deals For You'}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{flexDirection: 'row', marginHorizontal: 10}}>
            {topDeal.map((item, index) => (
              <TouchableOpacity
                onPress={() => {
                  props.navigation.navigate('ShopSingleProduct');
                }}
                key={index}
                style={{marginHorizontal: 8, marginTop: 12}}>
                <Image source={item.image} style={{height: 175, width: 137}} />
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#000000',
                    marginTop: 7,
                  }}>
                  {item.text}
                </Text>
                <View style={{flexDirection: 'row', gap: 3}}>
                  {[0, 0, 0, 0, 0].map((item, index) => (
                    <Image
                      key={index}
                      source={ImagePath.greenstar}
                      style={{height: 13, width: 13}}
                    />
                  ))}
                </View>
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                    textDecorationLine: '',
                  }}>
                  {'₹499  '}
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandRegular,
                      color: '#6C6C6C',
                      textDecorationLine: 'line-through',
                    }}>
                    {'₹899'}
                    <Text
                      style={{
                        fontSize: 12,
                        color: '#0A985F',
                        fontFamily: designeSheet.QuicksandSemiBold,
                        textDecorationLine: '',
                      }}>
                      {'  50% off'}
                    </Text>
                  </Text>
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
        <View style={{backgroundColor: '#E1EDFF', marginTop: 30}}>
          <View style={{flexDirection: 'row', justifyContent: 'space-evenly'}}>
            <Image
              source={ImagePath.shop11}
              style={{height: 43, width: 38, marginTop: -20}}
            />
            <Text
              style={{
                fontSize: 18,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
                alignSelf: 'center',
                marginTop: 10,
              }}>
              {'Best Pick , Best Price'}
            </Text>
            <Image
              source={ImagePath.shop12}
              style={{height: 43, width: 38, marginTop: -15}}
            />
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View
              style={{
                flexDirection: 'row',
                marginTop: 12,
                marginHorizontal: 15,
                gap: 18,
              }}>
              {bestPick.map((item, index) => (
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate('ShopSingleProduct');
                  }}
                  key={index}
                  style={{alignItems: 'center'}}>
                  <Image
                    source={item.imageData}
                    style={{height: 151, width: 101}}
                  />
                  <View
                    style={{
                      backgroundColor: '#1B5ABB',
                      borderRadius: 4,
                      marginTop: -12,
                    }}>
                    <Text
                      style={{
                        fontSize: 11,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#FFFFFF',
                        marginVertical: 6,
                        marginHorizontal: 5,
                      }}>
                      {'KURTIS + kURTAS'}
                    </Text>
                  </View>
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                      marginTop: 5,
                      marginBottom: 5,
                    }}>
                    {'Under ₹199'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
        <Text
          style={{
            fontSize: 18,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'TRENDS WITH BENEFITS'}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{flexDirection: 'row', marginHorizontal: 15, gap: 10}}>
            {trend.map((item, index) => (
              <TouchableOpacity
                onPress={() => {
                  props.navigation.navigate('ViewAll');
                }}
                key={index}
                style={{marginTop: 11}}>
                <ImageBackground
                  style={{flex: 1, height: 218, width: 178}}
                  source={ImagePath.shop18}>
                  <View
                    style={{
                      backgroundColor: 'white',
                      height: 142,
                      width: 147,
                      marginTop: 20,
                      alignSelf: 'center',
                      justifyContent: 'center',
                    }}>
                    <Image
                      source={item.image}
                      style={{height: 160, width: 147, marginTop: -18}}
                    />
                  </View>
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginTop: 5,
                    }}>
                    <Text
                      style={{
                        fontSize: 12,
                        color: '#FFFFFF',
                        fontFamily: designeSheet.QuicksandSemiBold,
                      }}>
                      {'NETPLAY'}
                    </Text>
                    <Text
                      style={{
                        fontSize: 15,
                        color: '#FFFFFF',
                        fontFamily: designeSheet.QuicksandSemiBold,
                      }}>
                      {'Under ₹899'}
                    </Text>
                    <Image
                      source={ImagePath.shop19}
                      style={{height: 12.1, width: 12.5}}
                    />
                  </View>
                </ImageBackground>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
        <Text
          style={{
            fontSize: 18,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'Exclusive Womenswear'}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{flexDirection: 'row', marginHorizontal: 15, gap: 20}}>
            {[0, 0, 0].map((item, index) => (
              <Image
                key={index}
                source={ImagePath.shop20}
                style={{
                  height: 161,
                  width: 355,
                  marginTop: 11,
                  borderRadius: 10,
                }}
              />
            ))}
          </View>
        </ScrollView>

        <Text
          style={{
            fontSize: 18,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'Treading Near You'}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{flexDirection: 'row', marginHorizontal: 10}}>
            {trendNear.map((item, index) => (
              <TouchableOpacity
                onPress={() => {
                  props.navigation.navigate('ShopSingleProduct');
                }}
                key={index}
                style={{marginHorizontal: 8, marginTop: 12}}>
                <Image source={item.image} style={{height: 175, width: 137}} />
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#000000',
                    marginTop: 7,
                  }}>
                  {item.text}
                </Text>
                <View style={{flexDirection: 'row', gap: 3}}>
                  {[0, 0, 0, 0, 0].map((item, index) => (
                    <Image
                      key={index}
                      source={ImagePath.greenstar}
                      style={{height: 13, width: 13}}
                    />
                  ))}
                </View>
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                    textDecorationLine: '',
                  }}>
                  {'₹499  '}
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandRegular,
                      color: '#6C6C6C',
                      textDecorationLine: 'line-through',
                    }}>
                    {'₹899'}
                    <Text
                      style={{
                        fontSize: 12,
                        color: '#0A985F',
                        fontFamily: designeSheet.QuicksandSemiBold,
                        textDecorationLine: '',
                      }}>
                      {'  50% off'}
                    </Text>
                  </Text>
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </ScrollView>
    </View>
  );
};

export default Shop;

const styles = StyleSheet.create({
  addressView: {
    flexDirection: 'row',
    alignItems: 'center',
    fontSize: 14,
    fontFamily: designeSheet.QuicksandBold,
    color: 'black',
    marginHorizontal: 20,
  },
  locationIcon: {
    marginLeft: 5,
    height: 20.41,
    width: 16,
  },
  addressTextView: {
    flexDirection: 'row',
    marginTop: 10,
    alignItems: 'center',
  },
  addressMainText: {
    marginLeft: 5,
    color: '#000000',
    fontFamily: designeSheet.QuicksandBold,
    fontSize: 14,
  },
  addressText: {
    marginLeft: 5,
    color: '#000000',
    fontSize: 13,
    fontFamily: designeSheet.QuicksandMedium,
  },
});
