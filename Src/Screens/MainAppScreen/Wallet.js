import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const Wallet = props => {
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Wallet'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View
        style={{
          borderWidth: 1,
          borderColor: '#CCCCCCCC',
          borderRadius: 16,
          marginHorizontal: 20,
          marginTop: 15,
        }}>
        <Text
          style={{
            fontSize: 14,
            fontFamily: designeSheet.QuicksandRegular,
            color: '#6C6C6C',
            alignSelf: 'center',
            marginTop: 24,
          }}>
          {'Total  Balance'}
        </Text>
        <Text
          style={{
            fontSize: 24,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            alignSelf: 'center',
          }}>
          {'₹1,235.05'}
        </Text>
        <View
          style={{marginHorizontal: 12, marginTop: 32, flexDirection: 'row'}}>
          <View style={{flex: 1}}>
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#6C6C6C',
              }}>
              {'Add Cash'}
            </Text>
            <Text
              style={{
                fontSize: 18,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {'₹ 12,939.03'}
            </Text>
          </View>
          <TouchableOpacity
          onPress={()=> {
            props.navigation.navigate('AddCash');
          }}
            style={{
              backgroundColor: '#019F48',
              borderRadius: 100,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text
              style={{
                fontSize: 18,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#FFFFFF',
                marginHorizontal: 20,
              }}>
              {'+  '}
              <Text
                style={{
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#FFFFFF',
                  marginHorizontal: 20,
                }}>
                {'Add Cash'}
              </Text>
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={{marginHorizontal: 12, marginTop: 32, flexDirection: 'row'}}>
          <View style={{flex: 1}}>
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#6C6C6C',
              }}>
              {'Withdrawal Amount'}
            </Text>
            <Text
              style={{
                fontSize: 18,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {'₹ 2,393.93'}
            </Text>
          </View>
          <TouchableOpacity
          onPress={()=> {
            props.navigation.navigate('Withdraw');
          }}
            style={{
              backgroundColor: '#7949FF',
              borderRadius: 100,
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
              paddingHorizontal: 20,
            }}>
            <Image source={ImagePath.down} style={{height: 16, width: 16}} />
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#FFFFFF',
              }}>
              {'  Withdraw'}
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={{
            marginHorizontal: 12,
            marginTop: 32,
            flexDirection: 'row',
            marginBottom: 28,
          }}>
          <View style={{flex: 1}}>
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#6C6C6C',
              }}>
              {'Cashback Reward'}
            </Text>
            <Text
              style={{
                fontSize: 18,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {'₹ 0'}
            </Text>
          </View>
        </View>
      </View>
      <TouchableOpacity
      onPress={()=> {
        props.navigation.navigate('TrasactionHistory');
      }}
        style={{
          borderWidth: 1,
          borderColor: '#CCCCCCCC',
          borderRadius: 16,
          marginHorizontal: 20,
          marginTop: 15,
        }}>
        <View style={{flexDirection: 'row', alignItems: 'center', padding: 10}}>
          <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
            <Image
              source={ImagePath.clocktimer}
              style={{height: 12, width: 12}}
            />
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#000000',
              }}>
              {'  See Transaction history'}
            </Text>
          </View>
          <Image
            source={ImagePath.homeimg4}
            style={{width: 16, height: 16, tintColor: '#000000'}}
          />
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default Wallet;

const styles = StyleSheet.create({});
