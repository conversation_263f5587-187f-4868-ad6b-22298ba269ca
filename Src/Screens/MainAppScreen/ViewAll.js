import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const ViewAll = props => {
  const [bestSeller, setBestSeller] = useState([
    {
      image: ImagePath.all1,
    },
    {
      image: ImagePath.all2,
    },
    {
      image: ImagePath.all1,
    },
    {
      image: ImagePath.all2,
    },
    {
      image: ImagePath.all1,
    },
    {
      image: ImagePath.all2,
    },
  ]);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'All Products'}
        carttrue={true}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <SafeAreaView style={{flex: 1, backgroundColor: '#FFF'}}>
        <ScrollView nestedScrollEnabled={true} style={{flex: 1}}>
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              alignSelf: 'center',
              marginTop: 18,
            }}>
            {'Bestseller'}
          </Text>
          <Text
            style={{
              fontSize: 12,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#1B5ABB',
              alignSelf: 'center',
            }}>
            {'View All'}
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View
              style={{
                flexDirection: 'row',
                marginHorizontal: 15,
                marginTop: 10,
              }}>
              {bestSeller.map((item, index) => (
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate('ShopSingleProduct');
                  }}
                  style={{marginHorizontal: 4}}
                  key={index}>
                  <Image
                    source={item.image}
                    style={{height: 148, width: 145}}
                  />
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandRegular,
                      color: '#000000',
                      width: 120,
                    }}>
                    {'Merdeco by madam g 105 Face serum | Skin Care'}
                  </Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        backgroundColor: '#0A985F',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: 2,
                      }}>
                      <Text
                        style={{
                          fontSize: 10,
                          fontFamily: designeSheet.QuicksandSemiBold,
                          color: '#FFFFFF',
                          marginHorizontal: 5,
                        }}>
                        {'4.9'}
                      </Text>
                      <Image
                        source={ImagePath.star}
                        style={{height: 9, width: 9, tintColor: 'white'}}
                      />
                    </View>
                    <Text
                      style={{
                        fontSize: 11,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#6C6C6C',
                        textDecorationLine: 'line-through',
                      }}>
                      {'₹899'}
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: designeSheet.QuicksandMedium,
                          color: '#000000',
                          textDecorationLine: '',
                        }}>
                        {' ₹499'}
                      </Text>
                    </Text>
                  </View>
                  <View
                    style={{
                      backgroundColor: '#000000',
                      height: 20,
                      width: 110,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: 3,
                      marginTop: 3,
                    }}>
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#FCFCFC',
                      }}>
                      {'Add to cart'}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              alignSelf: 'center',
              marginTop: 18,
            }}>
            {'Shop All Product'}
          </Text>

          <View
            style={{
              flexDirection: 'row',
              marginHorizontal: 15,
              marginTop: 10,
              flexWrap: 'wrap',
              gap: 5,
            }}>
            {bestSeller.map((item, index) => (
              <TouchableOpacity
                onPress={() => {
                  props.navigation.navigate('ShopSingleProduct');
                }}
                style={{marginHorizontal: 4}}
                key={index}>
                <Image source={item.image} style={{height: 169, width: 169}} />
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandRegular,
                    color: '#000000',
                    width: 120,
                  }}>
                  {'Merdeco by madam g 105 Face serum | Skin Care'}
                </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      backgroundColor: '#0A985F',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 2,
                    }}>
                    <Text
                      style={{
                        fontSize: 10,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#FFFFFF',
                        marginHorizontal: 5,
                      }}>
                      {'4.9'}
                    </Text>
                    <Image
                      source={ImagePath.star}
                      style={{height: 9, width: 9, tintColor: 'white'}}
                    />
                  </View>
                  <Text
                    style={{
                      fontSize: 11,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#6C6C6C',
                      textDecorationLine: 'line-through',
                    }}>
                    {'₹899'}
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#000000',
                        textDecorationLine: '',
                      }}>
                      {' ₹499'}
                    </Text>
                  </Text>
                </View>
                <View
                  style={{
                    backgroundColor: '#000000',
                    height: 20,
                    width: 110,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: 3,
                    marginTop: 3,
                  }}>
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#FCFCFC',
                    }}>
                    {'Add to cart'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default ViewAll;

const styles = StyleSheet.create({});
