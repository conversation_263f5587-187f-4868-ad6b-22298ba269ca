import {
  Animated,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const Register = props => {
  const headerHeight = useRef(new Animated.Value(0)).current;

  const [partner, setPartner] = useState([
    {
      img: ImagePath.r1,
      text1: 'Unlimited Earnings',
      text2: 'Start your earning from day one',
    },
    {
      img: ImagePath.r2,
      text1: 'Flexible Work Hours',
      text2: 'Enjoy the freedom to choose your working hours',
    },
    {
      img: ImagePath.r3,
      text1: '100% Safety',
      text2:
        'A dedicated safety team is available to assist in unsafe situations',
    },
    {
      img: ImagePath.r4,
      text1: 'No Profile Blocking',
      text2: 'Your profile will not be blocked',
    },
    {
      img: ImagePath.r5,
      text1: 'Govt. Certified Training',
      text2: 'Enhance your skills with certified training programs',
    },
    {
      img: ImagePath.r6,
      text1: 'Accidental Insurance',
      text2: 'Get ₹1 lakh coverage for unexpected events',
    },
    {
      img: ImagePath.r7,
      text1: 'Life Insurance',
      text2: 'Receive ₹5 lakh insurance coverage',
    },
    {
      img: ImagePath.r8,
      text1: 'Emergency Support',
      text2: 'Access personal loans in times of emergency',
    },
  ]);
  const [selectedTab, setSelectedTab] = useState('Female Categories');
  const [cat, setCat] = useState([
    {
      img: ImagePath.r10,
      text: 'Salon at home',
    },
    {
      img: ImagePath.r11,
      text: 'Spa at home',
    },
    {
      img: ImagePath.r12,
      text: 'Hydra treatments',
    },
    {
      img: ImagePath.r13,
      text: 'Laser treatment',
    },
    {
      img: ImagePath.r14,
      text: 'Mehndi at home',
    },
    {
      img: ImagePath.r15,
      text: 'Mehndi at home',
    },
    {
      img: ImagePath.r16,
      text: 'Makeup at home',
    },
    {
      img: ImagePath.r12,
      text: 'Salon at home',
    },
    {
      img: ImagePath.r14,
      text: 'Salon at home',
    },
  ]);
  const [form, setForm] = useState([
    {
      placeholder: 'Enter your name',
    },
    {
      placeholder: 'Enter your number',
    },
    {
      placeholder: 'Select your city',
    },
    {
      placeholder: 'What is your profession',
    },
    {
      placeholder: 'Female',
    },
  ]);
  const [editPopup, setEditPopup] = useState(false);
  const [name, setName] = useState('');

  useEffect(() => {
    Animated.timing(headerHeight, {
      toValue: 400,
      duration: 1000,
      useNativeDriver: false,
    }).start();
  }, []);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Register as Partner'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <ScrollView>
        <Text
          style={{
            fontSize: 14,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            marginHorizontal: 20,
            marginTop: 24,
          }}>
          {'Take Charge of Your Future'}
        </Text>
        <View
          style={{
            flexDirection: 'row',
            marginHorizontal: 20,
            marginTop: 13,
            alignItems: 'flex-start',
          }}>
          <View style={{flex: 1}}>
            <Text
              style={{
                fontSize: 22,
                fontFamily: designeSheet.QuicksandBold,
                color: '#AE014F',
              }}>
              {'Unlock earnings of ₹70,000/month'}
            </Text>
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#000000',
              }}>
              {
                'Join a growing network of over 5,000 partners in 55+ cities across India.'
              }
            </Text>
          </View>
          <Image
            source={ImagePath.part}
            style={{
              height: 160,
              width: 190,
            }}
          />
        </View>
        <View
          style={{
            borderWidth: 0.8,
            borderColor: '#CCCCCCCC',
          }}
        />

        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'Partners Love Working with'}
        </Text>
        <Image
          source={ImagePath.madamglogo}
          style={{
            height: 17,
            width: 100,
            alignSelf: 'center',
            marginTop: 5,
          }}
        />

        <View
          style={{
            flexDirection: 'row',
            marginHorizontal: 20,
            marginTop: 20,
            flexWrap: 'wrap',
            gap: 15,
          }}>
          {partner.map((item, index) => (
            <View
              key={index}
              style={{flexDirection: 'row', alignItems: 'flex-start'}}>
              <Image source={item.img} style={{height: 33, width: 33}} />
              <View style={{marginHorizontal: 10}}>
                <Text
                  style={{
                    fontSize: 13,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                    width: 100,
                  }}>
                  {item.text1}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandRegular,
                    color: '#6C6C6C',
                    width: 100,
                  }}>
                  {item.text2}
                </Text>
              </View>
            </View>
          ))}
        </View>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'Snaps from the Salon'}
        </Text>

        <Image
          source={ImagePath.r9}
          style={{
            height: 417,
            width: 339,
            alignSelf: 'center',
            marginTop: 15,
          }}
        />

        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandBold,
            color: '#000000',
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'Explore services now'}
        </Text>
        <View
          style={{
            flexDirection: 'row',
            marginTop: 20,
            alignSelf: 'center',
          }}>
          <TouchableOpacity onPress={() => setSelectedTab('Female Categories')}>
            <Text
              style={{
                fontSize: 15,
                color: '#000000',
                fontFamily: designeSheet.QuicksandSemiBold,
                marginHorizontal: 30,
                marginVertical: 10,
                textDecorationLine:
                  selectedTab === 'Female Categories' ? 'underline' : '',
                textDecorationStyle:
                  selectedTab === 'Female Categories' ? 'solid' : '',
              }}>
              {'Female Categories'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setSelectedTab('Male Categories')}>
            <Text
              style={{
                fontSize: 15,
                color: '#000000',
                fontFamily: designeSheet.QuicksandSemiBold,
                marginHorizontal: 30,
                marginVertical: 10,
                textDecorationLine:
                  selectedTab === 'Male Categories' ? 'underline' : '',
                textDecorationStyle:
                  selectedTab === 'Male Categories' ? 'solid' : '',
              }}>
              {'Male Categories'}
            </Text>
          </TouchableOpacity>
        </View>
        {selectedTab == 'Female Categories' ? (
          <View
            style={{
              marginHorizontal: 20,
              marginTop: 20,
              flexDirection: 'row',
              flexWrap: 'wrap',
              alignItems: 'center',
              gap: 20,
            }}>
            {cat.map((item, index) => (
              <View key={index} style={{}}>
                <Image source={item.img} style={{height: 53, width: 53}} />
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#313131',
                    width: 53,
                    textAlign: 'center',
                    marginTop: 3,
                  }}>
                  {item.text}
                </Text>
              </View>
            ))}
          </View>
        ) : (
          <View>
            <Text
              style={{
                fontSize: 15,
                fontFamily: designeSheet.QuicksandBold,
                color: 'black',
                alignSelf: 'center',
                marginTop: 20,
                marginBottom: 20,
              }}>
              {'No Data Insertion'}
            </Text>
          </View>
        )}
        <TouchableOpacity
          onPress={() => {
            setEditPopup(true);
          }}
          style={{
            backgroundColor: '#000000',
            padding: 10,
            borderRadius: 8,
            marginTop: 25,
            marginHorizontal: 20,
            marginBottom: 20,
          }}>
          <Text
            style={{
              color: 'white',
              textAlign: 'center',
              fontSize: 18,
              fontFamily: designeSheet.QuicksandSemiBold,
            }}>
            Register Now
          </Text>
        </TouchableOpacity>
      </ScrollView>
      {editPopup ? (
        <View
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            position: 'absolute',
            height: '100%',
            width: '100%',
            justifyContent: 'flex-end',
            gap: 20,
          }}>
          <Animated.View
            style={{
              height: headerHeight,
              width: '100%',
              backgroundColor: 'white',
              borderTopLeftRadius: 40,
              borderTopRightRadius: 40,
            }}>
            <ScrollView showsVerticalScrollIndicator={false} style={{}}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <View style={{flex: 1}}>
                  <Text
                    style={{
                      fontSize: 16,
                      color: '#000000',
                      fontFamily: designeSheet.QuicksandBold,
                      marginHorizontal: 20,
                      marginTop: 20,
                    }}>
                    {'Submit Your  Details & Get Started'}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => {
                    Animated.timing(headerHeight, {
                      toValue: 0,
                      duration: 400,
                      useNativeDriver: false,
                    }).start(() => {
                      setEditPopup(false);
                    });
                  }}>
                  <Image
                    source={ImagePath.crossimg}
                    style={{
                      height: 24,
                      width: 24,
                      marginTop: 20,
                      marginHorizontal: 20,
                    }}
                  />
                </TouchableOpacity>
              </View>
              <View style={{marginTop: 20}}>
                {form.map((item, index) => (
                  <View
                    style={{
                      borderColor: '#CCCCCC',
                      borderWidth: 1,
                      borderRadius: 8,
                      marginBottom: 12,
                      fontSize: 15,
                      fontFamily: designeSheet.QuicksandMedium,
                      marginHorizontal: 20,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}
                    key={index}>
                    <TextInput
                      style={{
                        flex: 1,
                        marginHorizontal: 5,
                        padding: 0,
                        marginVertical: 10,
                      }}
                      placeholder={item.placeholder}
                      value={name}
                      onChangeText={setName}
                      placeholderTextColor={'#6C6C6C'}
                    />
                    {index == 2 || index == 4 ? (
                      <Image
                        source={ImagePath.homeimg1}
                        style={{height: 14, width: 14, marginHorizontal: 5}}
                      />
                    ) : null}
                  </View>
                ))}
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginHorizontal: 20,
                }}>
                <Image
                  source={ImagePath.sum8}
                  style={{height: 14, width: 14, tintColor: '#CCCCCC'}}
                />
                <Text
                  style={{
                    marginHorizontal: 10,
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#6C6C6C',
                  }}>
                  {'I am comfortable for Home services'}
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  Animated.timing(headerHeight, {
                    toValue: 0,
                    duration: 400,
                    useNativeDriver: false,
                  }).start(() => {
                    setEditPopup(false);
                  });
                }}
                style={{
                  backgroundColor: '#000000',
                  borderRadius: 8,
                  marginTop: 20,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginHorizontal: 20,
                  marginBottom: 20,
                }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#FFFFFF',
                    marginVertical: 12,
                  }}>
                  {'Submit Details'}
                </Text>
              </TouchableOpacity>
            </ScrollView>
          </Animated.View>
        </View>
      ) : null}
    </View>
  );
};

export default Register;

const styles = StyleSheet.create({});
