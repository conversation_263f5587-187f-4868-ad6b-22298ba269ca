import {
  Animated,
  Image,
  ImageBackground,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Dimensions,
  FlatList,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import ImagePath from '../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../Designe/designeSheet';
import Loader from '../../../components/Loader';
import {
  addToCart,
  getCartItems,
  getCategoriesDetails,
  getPackages,
  getSubCatProducts,
  getTopSellingData,
} from "../../../Api's/Api";
import DynamicImage from '../../../components/DynamicImage';
import {API_URL, IMAGE_BASE_URL} from '../../../utils/urls';
import Editpopup from './Popups/Editpopup';
import ProductDetailsPopup from './Popups/ProductDetailsPopup';
import {calculateDiscountedPrice} from '../../../utils/CommonFunctions';
import Global from '../../../Globals/Global';
import CustomToast from '../../../components/CustomToast';
import EmptyListContent from '../../../components/EmptyListContent';
import StickyCartView from './StickyCartView';

const {width} = Dimensions.get('window');
const HEADER_MAX_HEIGHT = 290;
const HEADER_MIN_HEIGHT = 80;

const SingleServiceScreen = props => {
  const {slug, isPackage, bgImage, catName} = props.route.params;
  const [isLoading, setIsLoading] = useState(false);
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerHeight1 = useRef(new Animated.Value(0)).current;

  const flatListRef = useRef<FlatList>(null);

  const [service, setService] = useState([]);
  const [topSellingData, setTopSellingData] = useState([]);
  const [catProducts, setCatProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState();
  const [detailPopup, setDetailPopup] = useState(false);
  const [selectedSubCat, setSelectedSubCat] = useState(0);
  const [selectedCat, setSelectedCat] = useState('');
  const [toast, setToast] = useState(false);
  const [message, setMessage] = useState('');
  const [cartData, setCartData] = useState([]);
  const [selectedPackage, setSelectedPackage] = useState();

  const [packagesData, setPackagesData] = useState([]);

  const [cat, setCat] = useState([
    {
      text: '. Rica waxing - ',
      text1: 'Full Arms & Full Legs, Underarms',
    },
    {
      text: '. Premium Facial - ',
      text1: 'Korean Glow Facial',
    },
    {
      text: '. Manicure & Pedicure - ',
      text1: 'Mani - Pedi Combo',
    },
    {
      text: '. Facial Hair Removal - ',
      text1: 'Eyebrows (Threading)',
    },
  ]);

  const [editPopup, setEditPopup] = useState(false);

  async function getDetails() {
    setIsLoading(true);
    try {
      const result = await getCategoriesDetails(slug);
      if (result.status == 200) {
        // setEveryThingData(result.data.data);
        setService(result.data.data.subcategory);
        console.log('sdmnc dskjbckjdsnc', result.data.data.subcategory[0].slug);

        if (isPackage == 'inactive') {
          setSelectedCat(result.data.data.subcategory[0].name);
          subCatProducts(result.data.data.subcategory[0].slug);
        } else {
          setSelectedCat('Manageproduct');
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  async function subCatProducts(slug) {
    setIsLoading(true);
    try {
      const result = await getSubCatProducts(slug);
      if (result.status == 200) {
        setCatProducts(result.data.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  async function getTopSelling() {
    setIsLoading(true);
    try {
      const result = await getTopSellingData(slug);
      if (result.status == 200) {
        setTopSellingData(result.data.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  async function packages() {
    setIsLoading(true);
    try {
      const result = await getPackages(slug);
      if (result.status == 200) {
        setPackagesData(result.data.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    getCartItmes();
    getDetails();
    if (isPackage == 'active') {
      packages();
    }
  }, []);

  const headerHeight = scrollY.interpolate({
    inputRange: [0, HEADER_MAX_HEIGHT - HEADER_MIN_HEIGHT],
    outputRange: [HEADER_MAX_HEIGHT, HEADER_MIN_HEIGHT],
    extrapolate: 'clamp',
  });

  const imageTranslate = scrollY.interpolate({
    inputRange: [0, HEADER_MAX_HEIGHT],
    outputRange: [0, -50],
    extrapolate: 'clamp',
  });

  const stickyHeaderOpacity = scrollY.interpolate({
    inputRange: [HEADER_MAX_HEIGHT - 50, HEADER_MAX_HEIGHT],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  async function addInCart(item) {
    setIsLoading(true);
    var obj = {};

    obj =
      Global.isGuestUser == 'true'
        ? {
            itemType: 'products',
            itemRef: item._id,
            quantity: 1,
            category: 'beauty',
            guestId: Global.guest_id.toString(),
          }
        : {
            itemType: 'products',
            itemRef: item._id,
            quantity: 1,
            category: 'beauty',
            userId: Global.user_id.toString(),
          };

    console.log('sdjkbckjsbckjsb', obj);
    try {
      const result = await addToCart(obj);
      if (result.status == 200) {
        // setCatProducts(result.data.data.data);
        setMessage('Item added to cart.');
        setToast(true);
        // setTimeout(() => {
        //   props.navigation.navigate('BeautyCart');
        // }, 1000);
        getCartItmes();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  async function getCartItmes() {
    var type = Global.isGuestUser == 'true' ? 'guestId' : 'userId';
    var id = Global.isGuestUser == 'true' ? Global.guest_id : Global.user_id;
    try {
      var url = `${API_URL.getCartData}?${type}=${id}&category=beauty`;
      const result = await getCartItems(url);
      if (result.status == 200) {
        setCartData(result.data.items);
      }
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      {/* Parallax Header */}
      <Animated.View
        style={[
          styles.header,
          {
            height: headerHeight,
            transform: [{translateY: imageTranslate}],
          },
        ]}>
        <ImageBackground
          resizeMode="cover"
          style={styles.imageBackground}
          source={{uri: bgImage}}>
          <TouchableOpacity
            style={{
              marginLeft: 20,
              height: 40,
              width: 40,
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => props.navigation.goBack()}>
            <Image
              source={ImagePath.headerarrow}
              style={{height: 13, width: 20}}
            />
          </TouchableOpacity>

          <View
            style={{
              marginHorizontal: 20,
              marginTop: 20,
            }}>
            <Text style={styles.title}>Keratin</Text>
            <Text style={styles.subtitle}>infused care for your hair</Text>
          </View>
        </ImageBackground>
      </Animated.View>

      {/* Sticky Header */}
      <Animated.View
        style={[
          styles.stickyHeader,
          {
            opacity: stickyHeaderOpacity,
          },
        ]}>
        <View style={styles.stickyContent}>
          <TouchableOpacity onPress={() => props.navigation.goBack()}>
            <Image
              source={ImagePath.headerarrow}
              style={{height: 13, width: 20}}
            />
          </TouchableOpacity>
          <Text style={styles.stickyTitle}>{catName}</Text>
        </View>
      </Animated.View>

      {/* Scrollable Content */}
      <Animated.ScrollView
        scrollEventThrottle={16}
        contentContainerStyle={{paddingTop: HEADER_MAX_HEIGHT}}
        onScroll={Animated.event(
          [{nativeEvent: {contentOffset: {y: scrollY}}}],
          {useNativeDriver: false},
        )}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
            gap: 20,
            marginTop: 20,
          }}>
          <FlatList
            data={service}
            ListHeaderComponent={() =>
              isPackage == 'active' ? (
                <View style={{flex: 1, gap: 5, borderWidth: 0, width: 80}}>
                  <TouchableOpacity
                    onPress={() => {
                      setSelectedCat('Manageproduct');
                      packages();
                    }}
                    style={{
                      backgroundColor: '#88396B',
                      borderRadius: 12,
                      height: 54,
                      width: 54,
                      alignItems: 'center',
                      justifyContent: 'center',
                      alignSelf: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 9,
                        color: '#FFFFFF',
                        fontFamily: designeSheet.QuicksandSemiBold,
                        marginHorizontal: 10,
                        marginVertical: 10,
                      }}>
                      {'Manage your Package'}
                    </Text>
                  </TouchableOpacity>
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandBold,
                      color:
                        selectedCat == 'Manageproduct' ? '#88396B' : '#313131',
                      textAlign: 'center',
                    }}>
                    Manage your package
                  </Text>
                </View>
              ) : null
            }
            horizontal
            contentContainerStyle={{paddingHorizontal: 16}}
            renderItem={({item, index}) => (
              <View key={index} style={{gap: 5, borderWidth: 0, width: 80}}>
                <TouchableOpacity
                  onPress={() => {
                    subCatProducts(item.slug);
                    setSelectedCat(item.name);
                  }}
                  style={{
                    borderRadius: 10,
                    overflow: 'hidden',
                    height: 54,
                    width: 54,
                    alignSelf: 'center',
                    backgroundColor: 'white',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <DynamicImage
                    uri={`${IMAGE_BASE_URL}${item.image}`}
                    imgSize={54}
                    svgSize={54}
                  />
                </TouchableOpacity>

                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandBold,
                    color: selectedCat == item.name ? '#88396B' : '#313131',
                    textAlign: 'center',
                  }}>
                  {item.name}
                </Text>
              </View>
            )}
          />
        </View>

        {selectedCat != 'Manageproduct' ? (
          <FlatList
            data={catProducts}
            horizontal
            contentContainerStyle={{
              paddingHorizontal: 15,
              paddingTop: 10,
              gap: 10,
            }}
            renderItem={({item, index}) => (
              <TouchableOpacity
                onPress={() => {
                  setSelectedSubCat(index);
                  flatListRef.current?.scrollToOffset({
                    animated: true,
                    offset: 40,
                  });
                }}
                key={item._id}
                style={{
                  borderWidth: selectedSubCat == index ? 0 : 1,
                  padding: 4,
                  borderRadius: 6,
                  paddingHorizontal: 10,
                  backgroundColor:
                    selectedSubCat == index ? '#87005F' : 'transparent',
                }}>
                <Text
                  style={{
                    fontSize: 13,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: selectedSubCat == index ? 'white' : '#000000',
                    lineHeight: 20,
                  }}>
                  {item.subCategoryTypeName}
                </Text>
              </TouchableOpacity>
            )}
          />
        ) : null}

        {selectedCat == 'Manageproduct' ? (
          packagesData.length != 0 ? (
            <>
              <Text
                style={{
                  fontSize: 18,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#000000',
                  marginTop: 20,
                  marginHorizontal: 20,
                }}>
                {'Packages'}
              </Text>
              {packagesData.map((item, index) => (
                <View key={index}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginTop: 10,
                      marginHorizontal: 20,
                    }}>
                    <View style={{flex: 1}}>
                      <Text
                        style={{
                          fontSize: 16,
                          fontFamily: designeSheet.QuicksandSemiBold,
                          color: '#000000',
                        }}>
                        {item.name}
                      </Text>
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: designeSheet.QuicksandMedium,
                          color: '#6C6C6C',
                          marginBottom: 15,
                        }}>
                        {item.sub_title}
                      </Text>
                    </View>
                    {/* <TouchableOpacity
                      onPress={() => {
                        setEditPopup(true);
                        Animated.timing(headerHeight1, {
                          toValue: 600,
                          duration: 600,
                          useNativeDriver: false,
                        }).start();
                      }}>
                      <Image
                        source={ImagePath.editIcon}
                        style={{height: 18, width: 18}}
                      />
                    </TouchableOpacity> */}
                  </View>
                  {/* <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginHorizontal: 20,
                      marginTop: 10,
                    }}>
                    <Image
                      source={ImagePath.perimg}
                      style={{height: 14, width: 14}}
                    />
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#05945B',
                        marginHorizontal: 5,
                      }}>
                      {`${item.off_percentage}% OFF`}
                      <Text
                        style={{
                          color: '#6C6C6C',
                          fontSize: 12,
                          fontFamily: designeSheet.QuicksandRegular,
                          textDecorationLine: 'line-through',
                        }}>
                        {'  ₹4,499'}
                        <Text
                          style={{
                            color: '#000000',
                            fontSize: 16,
                            fontFamily: designeSheet.QuicksandSemiBold,
                            textDecorationLine: '',
                          }}>
                          {'  ₹2,499'}
                          <Text
                            style={{
                              color: '#6C6C6C',
                              fontSize: 12,
                              fontFamily: designeSheet.QuicksandMedium,
                              textDecorationLine: '',
                            }}>
                            {'  | 3 hr 15 min'}
                          </Text>
                        </Text>
                      </Text>
                    </Text>
                  </View> */}

                  {item.sub_points.map(points => (
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 4,
                        marginLeft: 20,
                        alignItems: 'flex-start',
                      }}>
                      <View
                        style={{
                          height: 5,
                          width: 5,
                          borderRadius: 2.5,
                          backgroundColor: 'black',
                          marginTop: 9,
                        }}
                      />

                      <Text
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandBold,
                          color: 'grey',
                          flexShrink: 1,
                        }}>
                        {points}
                      </Text>
                    </View>
                  ))}

                  <TouchableOpacity
                    onPress={() => {
                      console.log('dcndslkclkdnckdn', item);
                      setSelectedPackage(item);
                      setEditPopup(true);
                      Animated.timing(headerHeight1, {
                        toValue: 600,
                        duration: 600,
                        useNativeDriver: false,
                      }).start();
                    }}
                    style={{
                      backgroundColor: '#000000',
                      borderRadius: 12,
                      marginHorizontal: 20,
                      marginTop: 20,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 10,
                    }}>
                    <Text
                      style={{
                        fontSize: 16,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#FFFFFF',
                        marginVertical: 10,
                      }}>
                      {'Edit Package'}
                    </Text>
                  </TouchableOpacity>
                  {index == 2 ? null : (
                    <View
                      style={{
                        borderWidth: 0.5,
                        borderColor: '#6C6C6C',
                        marginHorizontal: 20,
                        marginTop: 20,
                      }}
                    />
                  )}
                </View>
              ))}
            </>
          ) : (
            <EmptyListContent
              text={'No Package Found!'}
              image={
                'https://cdn-icons-png.flaticon.com/128/17569/17569003.png'
              }
            />
          )
        ) : (
          <View onLayout={e => console.log('svshbvhbs', e)}>
            <FlatList
              ref={flatListRef}
              nestedScrollEnabled
              data={catProducts}
              ListEmptyComponent={() => (
                <EmptyListContent
                  text={'No Product Found!'}
                  image={
                    'https://cdn-icons-png.flaticon.com/128/17569/17569003.png'
                  }
                />
              )}
              renderItem={({item, index}) => (
                <View key={item._id} style={{gap: 10}}>
                  <Text
                    style={{
                      fontSize: 18,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                      marginTop: 20,
                      marginHorizontal: 20,
                    }}>
                    {item.subCategoryTypeName}
                  </Text>
                  {item.products.map((v, i) => (
                    <View
                      key={v._id}
                      style={{
                        backgroundColor: 'white',
                        marginHorizontal: 16,
                        borderRadius: 18,
                        overflow: 'hidden',
                        marginBottom: 10,
                        elevation: 5,
                        shadowOffset: {
                          height: 3,
                          width: 0,
                        },
                        shadowOpacity: 0.2,
                        shadowRadius: 10,
                        paddingBottom: 10,
                      }}>
                      <DynamicImage
                        uri={`${IMAGE_BASE_URL}${v.image}`}
                        imgHeight={200}
                        imgWidth={'100%'}
                      />
                      <View
                        style={{flexDirection: 'row', marginHorizontal: 10}}>
                        <View style={{flex: 1}}>
                          <Text
                            style={{
                              fontSize: 15,
                              fontFamily: designeSheet.QuicksandBold,
                              color: '#000000',
                              marginTop: 5,
                            }}>
                            {v.name}
                          </Text>
                          <Text
                            style={{
                              fontSize: 13,
                              fontFamily: designeSheet.QuicksandBold,
                              color: 'grey',
                            }}>
                            {v.sub_title}
                          </Text>
                        </View>
                        <TouchableOpacity
                          onPress={() => {
                            addInCart(v);
                          }}
                          activeOpacity={1}
                          style={{
                            paddingVertical: 5,
                            backgroundColor: 'black',
                            alignSelf: 'center',
                            borderRadius: 5,
                            width: '25%',
                          }}>
                          <Text
                            style={{
                              fontSize: 13,
                              fontFamily: designeSheet.QuicksandBold,
                              color: 'white',
                              alignSelf: 'center',
                            }}>
                            Add
                          </Text>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginHorizontal: 10,
                          marginTop: 10,
                          gap: 4,
                        }}>
                        <Text
                          style={{
                            color: '#000000',
                            fontSize: 14,
                            fontFamily: designeSheet.QuicksandBold,
                            textDecorationLine: 'none',
                          }}>
                          {'₹' +
                            calculateDiscountedPrice(
                              v.servicePrice,
                              v.off_percentage,
                            )}
                        </Text>
                        <Text
                          style={{
                            color: '#6C6C6C',
                            fontSize: 14,
                            fontFamily: designeSheet.QuicksandBold,
                            textDecorationLine: 'line-through',
                          }}>
                          {'₹' + v.servicePrice}
                        </Text>
                        <View
                          style={{
                            height: 12,
                            width: 1,
                            backgroundColor: 'grey',
                          }}
                        />
                        <Image
                          source={ImagePath.perimg}
                          style={{
                            height: 14,
                            width: 14,
                            resizeMode: 'cover',
                          }}
                        />
                        <Text
                          style={{
                            fontSize: 12,
                            fontFamily: designeSheet.QuicksandBold,
                            color: '#05945B',
                          }}>
                          {`${v.off_percentage}% OFF`}
                        </Text>
                      </View>

                      <View
                        style={{
                          height: 0.7,
                          backgroundColor: 'grey',
                          marginHorizontal: 16,
                          marginVertical: 10,
                        }}
                      />
                      {console.log('ckjnckjncn', v.sub_points == undefined)}
                      {v.sub_points != undefined
                        ? v.sub_points.map(points => (
                            <View
                              style={{
                                flexDirection: 'row',
                                gap: 4,
                                marginLeft: 10,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  height: 5,
                                  width: 5,
                                  borderRadius: 2.5,
                                  backgroundColor: 'black',
                                  marginTop: 3,
                                }}
                              />

                              <Text
                                style={{
                                  fontSize: 13,
                                  fontFamily: designeSheet.QuicksandBold,
                                  color: 'grey',
                                  lineHeight: 20,
                                }}>
                                {points}
                              </Text>
                            </View>
                          ))
                        : null}

                      <Text
                        onPress={() => {
                          setSelectedProduct(v);
                          setDetailPopup(true);
                          Animated.timing(headerHeight1, {
                            toValue: 600,
                            duration: 600,
                            useNativeDriver: false,
                          }).start();
                        }}
                        style={{
                          fontSize: 14,
                          fontFamily: designeSheet.QuicksandBold,
                          color: '#000000',
                          marginLeft: 10,
                          marginTop: 5,
                          textDecorationLine: 'underline',
                        }}>
                        {'View Details'}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            />
          </View>
        )}
        {[...Array(100)].map((_, i) => (
          <Text key={i} style={{margin: 10}}>
            Text{i}
          </Text>
        ))}
      </Animated.ScrollView>
      {editPopup ? (
        <Editpopup
          headerHeight={headerHeight1}
          setEditPopup={setEditPopup}
          selectedPackage={selectedPackage}
          getCartItmes={getCartItmes}
        />
      ) : null}

      {detailPopup ? (
        <ProductDetailsPopup
          headerHeight={headerHeight1}
          setDetailPopup={setDetailPopup}
          selectedProduct={selectedProduct}
        />
      ) : null}
      <Loader isActive={isLoading} />
      {toast && <CustomToast setToast={setToast} message={message} />}

      {cartData.length == 0 ? null : (
        <StickyCartView cartData={cartData} props={props} />
      )}
    </View>
  );
};

export default SingleServiceScreen;

const styles = StyleSheet.create({
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    overflow: 'hidden',
    zIndex: 2,
    width: '100%',
  },
  imageBackground: {
    flex: 1,
    width: width,
    justifyContent: 'flex-end',
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontFamily: designeSheet.QuicksandBold,
    color: '#87005F',
    marginTop: 30,
  },
  subtitle: {
    fontSize: 25,
    fontFamily: designeSheet.QuicksandBold,
    color: '#87005F',
    width: 200,
  },
  stickyHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: HEADER_MIN_HEIGHT,
    backgroundColor: '#FFF7FB',
    zIndex: 3,
    justifyContent: 'center',
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderColor: '#E0E0E0',
    paddingTop: StatusBar.currentHeight,
  },
  stickyContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stickyTitle: {
    fontSize: 16,
    color: '#87005F',
    fontFamily: designeSheet.QuicksandBold,
    textAlign: 'center',
    flex: 1,
  },
});
