import {
  Animated,
  Dimensions,
  FlatList,
  Image,
  LayoutAnimation,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  UIManager,
  View,
} from 'react-native';
import React, {FC, useRef, useState} from 'react';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../../Designe/designeSheet';
import {
  calculateDiscountedPrice,
  convertMinutesToHrMin,
} from '../../../../utils/CommonFunctions';
import DynamicImage from '../../../../components/DynamicImage';
import {IMAGE_BASE_URL} from '../../../../utils/urls';

if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

type EditPopupProps = {
  setDetailPopup: (value: boolean) => void;
  headerHeight: Animated.Value;
  selectedProduct: any;
};

const ProductDetailsPopup: FC<EditPopupProps> = ({
  setDetailPopup,
  headerHeight,
  selectedProduct,
}) => {
  const [expandedTopic, setExpandedTopic] = useState(null);
  const [animatedValues, setAnimatedValues] = useState({});
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scrollX = useRef(new Animated.Value(0)).current;

  const scaleAnim = useRef(new Animated.Value(1)).current;

  const toggleExpand = (id: String | Number) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);

    // Initialize animated value if not exists
    if (!animatedValues[id]) {
      animatedValues[id] = new Animated.Value(0);
      setAnimatedValues({...animatedValues});
    }

    const isExpanded = expandedTopic === id;
    const previousExpandedTopic = expandedTopic;

    // Reset the previous expanded topic's animation
    if (
      previousExpandedTopic &&
      previousExpandedTopic !== id &&
      animatedValues[previousExpandedTopic]
    ) {
      Animated.timing(animatedValues[previousExpandedTopic], {
        toValue: 0, // Reset rotation
        duration: 200,
        useNativeDriver: true,
      }).start();
    }

    // Toggle the new topic and animate the arrow icon
    setExpandedTopic(isExpanded ? null : id);
    Animated.timing(animatedValues[id], {
      toValue: isExpanded ? 0 : 1, // 0 = collapsed, 1 = expanded
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const animateFade = () => {
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateZoom = () => {
    scaleAnim.setValue(0.9);
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      friction: 5,
    }).start();
  };

  //   const handleScroll = direction => {
  //     let newIndex = direction === 'left' ? currentIndex - 1 : currentIndex + 1;

  //     if (newIndex >= 0 && newIndex < selectedProduct.procedures.length) {
  //       // Fade out current
  //       Animated.timing(fadeAnim, {
  //         toValue: 0,
  //         duration: 200,
  //         useNativeDriver: true,
  //       }).start(() => {
  //         // Scroll to next/prev with animation
  //         flatListRef.current?.scrollToIndex({index: newIndex, animated: true});

  //         // Wait a bit for slide animation to settle before fade-in
  //         setTimeout(() => {
  //           setCurrentIndex(newIndex); // Update index
  //           Animated.timing(fadeAnim, {
  //             toValue: 1,
  //             duration: 200,
  //             useNativeDriver: true,
  //           }).start();
  //         }, 200); // Adjust delay if needed
  //       });
  //     }
  //   };

  const handleNext = () => {
    if (currentIndex < selectedProduct.procedures.length - 1) {
      const newIndex = currentIndex + 1;
      flatListRef.current?.scrollToIndex({index: newIndex, animated: true});
      setCurrentIndex(newIndex);
      animateZoom();
    }
  };

  const handlePrev = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      flatListRef.current?.scrollToIndex({index: newIndex, animated: true});
      setCurrentIndex(newIndex);
      animateZoom();
    }
  };

  const onMomentumScrollEnd = e => {
    const index = Math.round(
      e.nativeEvent.contentOffset.x / Dimensions.get('screen').width,
    );
    console.log('isjkbkjbc', index);
    setCurrentIndex(index);
  };

  return (
    <View
      style={{
        backgroundColor: 'rgba(0,0,0,0.6)',
        position: 'absolute',
        height: '100%',
        width: '100%',
        justifyContent: 'flex-end',
        gap: 20,
        zIndex: 4,
      }}>
      <TouchableOpacity
        style={{marginBottom: -10}}
        onPress={() => {
          Animated.timing(headerHeight, {
            toValue: 0,
            duration: 400,
            useNativeDriver: false,
          }).start(() => {
            setDetailPopup(false);
          });
        }}>
        <Image
          source={ImagePath.crossimg}
          style={{
            height: 32,
            width: 32,
            marginTop: 20,
            marginHorizontal: 20,
            tintColor: 'white',
            alignSelf: 'flex-end',
          }}
        />
      </TouchableOpacity>
      <Animated.View
        style={{
          height: headerHeight,
          width: '100%',
          backgroundColor: 'white',
          borderTopLeftRadius: 35,
          borderTopRightRadius: 35,
          overflow: 'hidden',
        }}>
        <ScrollView>
          <View style={{flex: 1}}>
            <Text
              style={{
                fontSize: 22,
                color: '#000000',
                fontFamily: designeSheet.QuicksandBold,
                marginHorizontal: 20,
                marginTop: 20,
              }}>
              {selectedProduct.name}
            </Text>
            {selectedProduct.type == 'single' ? (
              <Text
                style={{
                  fontSize: 13,
                  fontFamily: designeSheet.QuicksandBold,
                  color: 'grey',
                  marginLeft: 20,
                }}>
                {selectedProduct.sub_title}
              </Text>
            ) : (
              <Text
                style={{
                  fontSize: 13,
                  fontFamily: designeSheet.QuicksandBold,
                  color: '#000000',
                  marginLeft: 20,
                }}>
                {'Starts at ₹' + selectedProduct.servicePrice}
              </Text>
            )}
          </View>

          {selectedProduct.duration != '' ? (
            <Text
              style={{
                fontSize: 13,
                fontFamily: designeSheet.QuicksandBold,
                color: 'grey',
                marginLeft: 20,
              }}>
              {convertMinutesToHrMin(selectedProduct.duration)}
            </Text>
          ) : null}

          {selectedProduct.type == 'single' ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginHorizontal: 20,
                marginTop: 10,
                gap: 4,
              }}>
              <Text
                style={{
                  color: '#000000',
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandBold,
                  textDecorationLine: 'none',
                }}>
                {'₹' +
                  calculateDiscountedPrice(
                    selectedProduct.servicePrice,
                    selectedProduct.off_percentage,
                  )}
              </Text>
              <Text
                style={{
                  color: '#6C6C6C',
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandBold,
                  textDecorationLine: 'line-through',
                }}>
                {'₹' + selectedProduct.servicePrice}
              </Text>
              <View style={{height: 12, width: 1, backgroundColor: 'grey'}} />
              <Image
                source={ImagePath.perimg}
                style={{
                  height: 14,
                  width: 14,
                  resizeMode: 'cover',
                }}
              />
              <Text
                style={{
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandBold,
                  color: '#05945B',
                }}>
                {`${selectedProduct.off_percentage}% OFF`}
              </Text>
            </View>
          ) : (
            <View style={{}}>
              {selectedProduct.addOns.map((item, index) => (
                <View
                  style={{
                    flexDirection: 'row',
                    marginHorizontal: 20,
                    borderBottomWidth: 0.5,
                    paddingVertical: 20,
                    borderColor: 'grey',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                  <View>
                    <Text
                      style={{
                        fontSize: 15,
                        fontFamily: designeSheet.QuicksandBold,
                        color: 'black',
                      }}>
                      {item.name}
                    </Text>
                    <Text
                      style={{
                        fontSize: 13,
                        fontFamily: designeSheet.QuicksandBold,
                        color: 'grey',
                      }}>
                      {item.shortDescription}
                    </Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: 10,
                        gap: 4,
                      }}>
                      <Text
                        style={{
                          color: '#000000',
                          fontSize: 14,
                          fontFamily: designeSheet.QuicksandBold,
                          textDecorationLine: 'none',
                        }}>
                        {'₹' +
                          calculateDiscountedPrice(
                            item.servicePrice,
                            selectedProduct.off_percentage,
                          )}
                      </Text>
                      <Text
                        style={{
                          color: '#6C6C6C',
                          fontSize: 14,
                          fontFamily: designeSheet.QuicksandBold,
                          textDecorationLine: 'line-through',
                        }}>
                        {'₹' + item.servicePrice}
                      </Text>
                      <View
                        style={{height: 12, width: 1, backgroundColor: 'grey'}}
                      />
                      <Image
                        source={ImagePath.perimg}
                        style={{
                          height: 14,
                          width: 14,
                          resizeMode: 'cover',
                        }}
                      />
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: designeSheet.QuicksandBold,
                          color: '#05945B',
                        }}>
                        {`${selectedProduct.off_percentage}% OFF`}
                      </Text>
                    </View>
                  </View>
                  <View style={{flex: 0.53}}>
                    <Image
                      style={{
                        flex: 0.53,
                        height: 100,
                        borderRadius: 20,
                      }}
                      source={{uri: `${IMAGE_BASE_URL}${item.image}`}}
                    />
                    <TouchableOpacity
                      activeOpacity={1}
                      style={{
                        paddingVertical: 5,
                        position: 'absolute',
                        backgroundColor: 'black',
                        alignSelf: 'center',
                        width: '60%',
                        borderRadius: 5,
                        bottom: -10,
                      }}>
                      <Text
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandBold,
                          color: 'white',
                          alignSelf: 'center',
                        }}>
                        Add
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          )}

          {selectedProduct.overview == undefined ? null : (
            <View style={{paddingVertical: 30}}>
              <View
                style={{
                  flexDirection: 'row',
                  gap: 5,
                  marginHorizontal: 20,
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                <View
                  style={{
                    width: 3,
                    backgroundColor: 'black',
                    height: 17,
                    marginTop: 2,
                  }}
                />
                <Text
                  style={{
                    fontSize: 17,
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandBold,
                  }}>
                  {'Overview'}
                </Text>
              </View>

              <FlatList
                data={selectedProduct.overview}
                numColumns={2}
                keyExtractor={item => item._id}
                contentContainerStyle={{paddingHorizontal: 20}}
                columnWrapperStyle={{gap: 20}}
                renderItem={({item, index}) => (
                  <View
                    style={{
                      width: Dimensions.get('screen').width / 2 - 30,
                      borderRadius: 20,
                      overflow: 'hidden',
                    }}>
                    <Image
                      style={{
                        height: 150,
                        width: '100%',
                        borderRadius: 20,
                      }}
                      source={{uri: `${IMAGE_BASE_URL}${item.image}`}}
                    />
                  </View>
                )}
              />
            </View>
          )}

          <View style={{paddingVertical: 30}}>
            <View
              style={{
                flexDirection: 'row',
                gap: 5,
                marginHorizontal: 20,
                alignItems: 'center',
                marginBottom: 20,
              }}>
              <View
                style={{
                  width: 3,
                  backgroundColor: 'black',
                  height: 17,
                  marginTop: 2,
                }}
              />
              <Text
                style={{
                  fontSize: 17,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandBold,
                }}>
                {'Procedure'}
              </Text>
            </View>

            <View style={{flexDirection: 'row'}}>
              <TouchableOpacity
                onPress={() => handlePrev()}
                style={{
                  position: 'absolute',
                  height: 30,
                  width: 30,
                  borderRadius: 15,
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  zIndex: 10,
                  alignSelf: 'center',
                  left: 5,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Image
                  style={{
                    height: 12,
                    width: 12,
                    tintColor: 'white',
                  }}
                  source={{
                    uri: `https://cdn-icons-png.flaticon.com/128/271/271220.png`,
                  }}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleNext()}
                style={{
                  position: 'absolute',
                  height: 30,
                  width: 30,
                  borderRadius: 15,
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  zIndex: 10,
                  alignSelf: 'center',
                  right: 5,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Image
                  style={{
                    height: 12,
                    width: 12,
                    tintColor: 'white',
                  }}
                  source={{
                    uri: `https://cdn-icons-png.flaticon.com/128/271/271228.png`,
                  }}
                />
              </TouchableOpacity>

              <FlatList
                ref={flatListRef}
                data={selectedProduct.procedures}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                keyExtractor={(_, i) => i.toString()}
                onMomentumScrollEnd={onMomentumScrollEnd}
                onScroll={Animated.event(
                  [{nativeEvent: {contentOffset: {x: scrollX}}}],
                  {useNativeDriver: false},
                )}
                renderItem={({item, index}) => {
                  const inputRange = [
                    (index - 1) * Dimensions.get('window').width,
                    index * Dimensions.get('window').width,
                    (index + 1) * Dimensions.get('window').width,
                  ];

                  const scale = scrollX.interpolate({
                    inputRange,
                    outputRange: [0.6, 1, 0.6],
                    extrapolate: 'clamp',
                  });
                  return (
                    <Animated.View
                      style={{
                        width: Dimensions.get('screen').width,
                        opacity: fadeAnim,
                        transform: [{scale}],
                      }}>
                      <View
                        style={{
                          borderRadius: 10,
                          width: '80%',
                          alignSelf: 'center',
                          overflow: 'hidden',
                          alignItems: 'center',
                          backgroundColor: 'white',
                          paddingBottom: 15,
                          // elevation: 1,
                          marginBottom: 10,
                        }}>
                        <Image
                          style={{
                            height: 200,
                            width: '100%',
                          }}
                          source={{uri: `${IMAGE_BASE_URL}${item.image}`}}
                        />

                        <Text
                          style={{
                            fontSize: 15,
                            color: '#000000',
                            fontFamily: designeSheet.QuicksandBold,
                          }}>
                          {item.title}
                        </Text>
                        <Text
                          style={{
                            fontSize: 13,
                            fontFamily: designeSheet.QuicksandBold,
                            color: 'grey',
                            textAlign: 'center',
                          }}>
                          {item.description}
                        </Text>
                      </View>
                    </Animated.View>
                  );
                }}
              />
            </View>
            <View
              style={{
                padding: 2,
                backgroundColor: 'black',
                alignSelf: 'center',
                paddingHorizontal: 5,
                borderRadius: 5,
                marginTop: 20,
              }}>
              <Text
                style={{
                  fontSize: 13,
                  fontFamily: designeSheet.QuicksandBold,
                  color: 'white',
                  textAlign: 'center',
                  lineHeight: 20,
                }}>
                {`${currentIndex + 1}/${selectedProduct.procedures.length}`}
              </Text>
            </View>
          </View>

          <View style={{gap: 10}}>
            {selectedProduct.dynamicData.map((item: any, index: number) => (
              <View style={{gap: 5}}>
                <View
                  style={{
                    flexDirection: 'row',
                    gap: 5,
                    marginHorizontal: 20,
                    alignItems: 'center',
                  }}>
                  <View
                    style={{
                      width: 3,
                      backgroundColor: 'black',
                      height: 17,
                      marginTop: 2,
                    }}
                  />
                  <Text
                    style={{
                      fontSize: 17,
                      color: '#000000',
                      fontFamily: designeSheet.QuicksandBold,
                    }}>
                    {item.key}
                  </Text>
                </View>

                {item.values.map((points: any) => (
                  <View
                    style={{
                      flexDirection: 'row',
                      gap: 4,
                      marginHorizontal: 20,
                      alignItems: 'flex-start',
                    }}>
                    <View
                      style={{
                        height: 5,
                        width: 5,
                        borderRadius: 2.5,
                        backgroundColor: 'black',
                        marginTop: 8,
                      }}
                    />
                    <Text
                      style={{
                        fontSize: 13,
                        fontFamily: designeSheet.QuicksandBold,
                        color: 'grey',
                        lineHeight: 20,
                      }}>
                      {points}
                    </Text>
                  </View>
                ))}
              </View>
            ))}
          </View>

          {selectedProduct.faq.length == 0 ? null : (
            <View>
              <View
                style={{
                  flexDirection: 'row',
                  gap: 5,
                  marginHorizontal: 20,
                  alignItems: 'center',
                  marginVertical: 10,
                }}>
                <View
                  style={{
                    width: 3,
                    backgroundColor: 'black',
                    height: 17,
                    marginTop: 2,
                  }}
                />
                <Text
                  style={{
                    fontSize: 17,
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandBold,
                  }}>
                  {'FAQ'}
                </Text>
              </View>
              <FlatList
                data={selectedProduct.faq}
                keyExtractor={item => item.id}
                contentContainerStyle={{paddingHorizontal: 20}}
                renderItem={({item, index}) => {
                  const rotateAnimation = animatedValues[item.question]
                    ? animatedValues[item.question].interpolate({
                        inputRange: [0, 1],
                        outputRange: ['270deg', '360deg'], // Rotate arrow down when expanded
                      })
                    : '270deg';

                  return (
                    <TouchableOpacity
                      activeOpacity={0.8}
                      onPress={() => toggleExpand(item.question)}
                      style={styles.topic}>
                      <View style={styles.topicHeader}>
                        <Text style={styles.topicText}>{item.question}</Text>
                        <Animated.Image
                          source={{
                            uri: 'https://cdn-icons-png.flaticon.com/128/2985/2985150.png',
                          }}
                          style={{
                            height: 15,
                            width: 15,
                            transform: [{rotate: rotateAnimation}],
                          }}
                        />
                      </View>
                      {expandedTopic === item.question && (
                        <Text style={styles.topicDescription}>
                          {item.answer}
                        </Text>
                      )}
                    </TouchableOpacity>
                  );
                }}
              />
            </View>
          )}
        </ScrollView>
      </Animated.View>
    </View>
  );
};

export default ProductDetailsPopup;

const styles = StyleSheet.create({
  topic: {
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 10,
    elevation: 5,
    shadowOffset: {height: 0, width: 3},
    shadowOpacity: 0.3,
    shadowColor: 'black',
  },
  topicHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  topicText: {
    fontSize: 13,
    fontFamily: designeSheet.QuicksandSemiBold,
    color: 'black',
  },
  topicDescription: {
    fontSize: 12,
    color: 'black',
    marginTop: 10,
    lineHeight: 20,
    fontFamily: designeSheet.QuicksandRegular,
  },
});
