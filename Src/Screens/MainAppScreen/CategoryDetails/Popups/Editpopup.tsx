import {
  View,
  Text,
  Animated,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import designeSheet from '../../../../Designe/designeSheet';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import {
  calculateDiscountedPrice,
  convertMinutesToHrMin,
} from '../../../../utils/CommonFunctions';
import ServiceProductListPopup from './ServiceProductListPopup';
import Global from '../../../../Globals/Global';
import Loader from '../../../../components/Loader';
import CustomToast from '../../../../components/CustomToast';
import {addToCart} from "../../../../Api's/Api";

type EditPopupProps = {
  setEditPopup: (value: boolean) => void;
  headerHeight: Animated.Value;
  selectedPackage: any;
  getCartItmes: () => {};
};

const Editpopup: React.FC<EditPopupProps> = ({
  setEditPopup,
  headerHeight,
  selectedPackage,
  getCartItmes,
}) => {
  const [selectedServices, setSelectedServices] = useState<{
    [key: string]: boolean;
  }>({});

  const [selectiveIndex, setSelectiveIndex] = useState(0);
  const [productPopup, setProductPopup] = useState(false);
  const [productList, setProductList] = useState([]);
  const [forName, setForName] = useState('');
  const [forName1, setForName1] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [toast, setToast] = useState(false);
  const [message, setMessage] = useState('');

  const [customItems, setCustomItems] = useState<
    {serviceRef: any; selectedProduct: number}[]
  >([]);

  const headerHeight1 = useRef(new Animated.Value(0)).current;
  const [selectedProducts, setSelectedProducts] = useState<{
    [key: string]: number;
  }>({});

  useEffect(() => {
    console.log('Updated customItems:', customItems);
    console.log('Upadate selected Products', selectedProducts);
  }, [customItems, selectedProducts]);

  const calculateTotalPrice = () => {
    let total = 0;

    selectedPackage.connections.forEach((category, catIndex) => {
      category.services.forEach((service, serviceIndex) => {
        const isSelected = selectedServices[service._id];
        if (isSelected) {
          const key = `${service._id}-${serviceIndex}`;
          const selectedIndex = selectedProducts[key] ?? 0;
          const selectedProduct = service.products[selectedIndex];

          if (selectedProduct?.price) {
            total += selectedProduct.price;
          }
        }
      });
    });

    return total;
  };

  async function addInCart() {
    setIsLoading(true);
    var obj = {};

    obj =
      Global.isGuestUser == 'true'
        ? {
            itemType: 'beauty_packages',
            itemRef: selectedPackage._id,
            quantity: 1,
            category: 'beauty',
            guestId: Global.guest_id.toString(),
            customItems: customItems,
          }
        : {
            itemType: 'beauty_packages',
            itemRef: selectedPackage._id,
            quantity: 1,
            category: 'beauty',
            userId: Global.user_id.toString(),
            customItems: customItems,
          };

    console.log('sdjkbckjsbckjsb', obj);
    try {
      const result = await addToCart(obj);
      if (result.status == 200) {
        // setCatProducts(result.data.data.data);
        setMessage('Item added to cart.');
        setToast(true);
        getCartItmes();
        Animated.timing(headerHeight, {
          toValue: 0,
          duration: 400,
          useNativeDriver: false,
        }).start(() => {
          setEditPopup(false);
        });
        // setTimeout(() => {
        //   props.navigation.navigate('BeautyCart');
        // }, 1000);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  const calculateTotalTime = () => {
    let totalMinutes = 0;

    selectedPackage.connections.forEach(category => {
      category.services.forEach(service => {
        if (selectedServices[service._id]) {
          totalMinutes += service.duration || 0;
        }
      });
    });

    return totalMinutes == 0 ? '0 min' : convertMinutesToHrMin(totalMinutes);
  };

  return (
    <View
      style={{
        backgroundColor: 'rgba(0,0,0,0.5)',
        position: 'absolute',
        height: '100%',
        width: '100%',
        justifyContent: 'flex-end',
        gap: 20,
        zIndex: 4,
      }}>
      <Animated.View
        style={{
          height: headerHeight,
          width: '100%',
          backgroundColor: '#FFF7FB',
          borderTopLeftRadius: 40,
          borderTopRightRadius: 40,
        }}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <View style={{flex: 1}}>
            <Text
              style={{
                fontSize: 16,
                color: '#000000',
                fontFamily: designeSheet.QuicksandBold,
                marginHorizontal: 15,
                marginTop: 20,
              }}>
              {'Make your Package'}
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              Animated.timing(headerHeight, {
                toValue: 0,
                duration: 400,
                useNativeDriver: false,
              }).start(() => {
                setEditPopup(false);
              });
            }}>
            <Image
              source={ImagePath.crossimg}
              style={{
                height: 24,
                width: 24,
                marginTop: 20,
                marginHorizontal: 20,
              }}
            />
          </TouchableOpacity>
        </View>
        <View
          style={{
            borderWidth: 0.5,
            borderColor: '#A7A7A7',
            marginHorizontal: 15,
            marginVertical: 10,
          }}
        />

        {/* Categories */}
        <View>
          <FlatList
            data={selectedPackage.connections}
            horizontal
            keyExtractor={(_, index) => index.toString()}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{paddingHorizontal: 15}}
            renderItem={({item, index}) => (
              <TouchableOpacity
                onPress={() => {
                  setSelectiveIndex(index);
                }}
                style={{
                  borderWidth: 1,
                  backgroundColor:
                    selectiveIndex === index ? '#000000' : '#FFFFFF',
                  borderColor: '#CCCCCC',
                  padding: 10,
                  borderRadius: 8,
                  marginHorizontal: 5,
                  height: 40,
                }}>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: selectiveIndex === index ? '#FFFFFF' : '#000000',
                    lineHeight: 20,
                  }}>
                  {item.serviceType.name}
                </Text>
              </TouchableOpacity>
            )}
          />
        </View>
        <View
          style={{
            borderWidth: 0.5,
            borderColor: '#A7A7A7',
            marginHorizontal: 15,
            marginTop: 10,
          }}
        />

        <Text
          style={{
            fontSize: 13,
            color: '#000000',
            fontFamily: designeSheet.QuicksandBold,
            marginHorizontal: 15,
            marginTop: 10,
            marginBottom: 5,
          }}>
          Total Service Time: {calculateTotalTime()}
        </Text>

        {/* Categories List */}
        <FlatList
          data={selectedPackage.connections}
          keyExtractor={(_, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          renderItem={({item, index}) => (
            <View>
              <Text
                style={{
                  fontSize: 16,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandBold,
                  marginHorizontal: 15,
                  marginTop: 10,
                  marginBottom: 5,
                }}>
                {item.serviceType.name}
              </Text>

              {/* Inner Services FlatList (OR use map) */}
              {item.services.map((v: any, i: Number) => (
                <View
                  style={{
                    flexDirection: 'row',
                    marginHorizontal: 15,
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: 10,
                  }}>
                  <TouchableOpacity
                    style={{flex: 1}}
                    onPress={() => {
                      const key = v._id;

                      setSelectedServices(prev => {
                        const updated = {...prev};

                        if (updated[key]) {
                          delete updated[key]; // unselect

                          // Remove from customItems
                          setCustomItems(prev =>
                            prev.filter(item => item.serviceRef !== key),
                          );
                        } else {
                          updated[key] = true; // select

                          // Add to customItems
                          setCustomItems(prev => [
                            ...prev,
                            {serviceRef: key, selectedProduct: 0},
                          ]);
                        }
                        return updated;
                      });
                    }}
                    key={i.toString()}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Image
                        source={
                          selectedServices[v._id]
                            ? ImagePath.yesCheck
                            : ImagePath.box
                        }
                        style={{
                          height: 19,
                          width: 18,
                          resizeMode: 'contain',
                        }}
                      />
                      <Text
                        style={{
                          fontSize: 14,
                          fontFamily: designeSheet.QuicksandSemiBold,
                          color: '#000000',
                          marginHorizontal: 5,
                        }}>
                        {v.name}
                      </Text>
                    </View>

                    {/* Price and Duration */}
                    <Text style={{}}>
                      <Text
                        style={{
                          fontSize: 14,
                          color: '#000000',
                          fontFamily: designeSheet.QuicksandSemiBold,
                          marginTop: 5,
                        }}>
                        {`₹${
                          v.products[selectedProducts[`${v._id}-${i}`] || 0]
                            ?.price
                        }`}
                      </Text>

                      {'  |  '}
                      <Text
                        style={{
                          fontSize: 12,
                          color: '#6C6C6C',
                          fontFamily: designeSheet.QuicksandMedium,
                        }}>
                        {convertMinutesToHrMin(v.duration)}
                      </Text>
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    disabled={!selectedServices[v._id]}
                    // On product name click
                    onPress={() => {
                      setForName1(v.name);
                      const key = `${v._id}-${i}`;

                      // Ensure default product selected for the current service
                      setSelectedProducts(prev => {
                        if (prev[key] === undefined) {
                          return {...prev, [key]: 0};
                        }
                        return prev;
                      });

                      setProductList(v.products);
                      setProductPopup(true);
                      setForName(key);
                      Animated.timing(headerHeight1, {
                        toValue: 200,
                        duration: 600,
                        useNativeDriver: false,
                      }).start();
                    }}
                    style={{
                      borderWidth: 1,
                      padding: 4,
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 5,
                      justifyContent: 'space-between',
                      width: 100,
                    }}>
                    <Text
                      numberOfLines={1}
                      style={{
                        fontSize: 14,
                        color: '#000000',
                        fontFamily: designeSheet.QuicksandSemiBold,
                        flex: 1,
                      }}>
                      {v.products[selectedProducts[`${v._id}-${i}`] || 0]?.name}
                    </Text>

                    <Image
                      source={{
                        uri: 'https://cdn-icons-png.flaticon.com/128/32/32195.png',
                      }}
                      style={{
                        height: 10,
                        width: 10,
                        resizeMode: 'contain',
                      }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
        />
        <View
          style={{
            padding: 20,
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: 'white',
            justifyContent: 'space-between',
          }}>
          <View>
            <Text>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandBold,
                  color: 'black',
                }}>
                ₹
                {Math.floor(
                  calculateDiscountedPrice(
                    calculateTotalPrice(),
                    selectedPackage.off_percentage,
                  ),
                )}
                {'  '}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandBold,
                  color: 'grey',
                  textDecorationLine: 'line-through',
                }}>
                ₹{calculateTotalPrice()}
              </Text>
            </Text>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandBold,
                color: 'green',
              }}>
              Save ₹
              {calculateTotalPrice() -
                calculateDiscountedPrice(
                  calculateTotalPrice(),
                  selectedPackage.off_percentage,
                )}
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              addInCart();
            }}
            disabled={calculateTotalPrice() == 0 ? true : false}
            activeOpacity={0.5}
            style={{
              paddingVertical: 10,
              backgroundColor: 'black',
              alignSelf: 'center',
              borderRadius: 5,
              paddingHorizontal: 10,
              opacity: calculateTotalPrice() == 0 ? 0.5 : 1,
            }}>
            <Text
              style={{
                fontSize: 13,
                fontFamily: designeSheet.QuicksandBold,
                color: 'white',
                alignSelf: 'center',
              }}>
              Add To Cart
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
      {productPopup ? (
        <ServiceProductListPopup
          headerHeight={headerHeight1}
          productList={productList}
          setProductPopup={setProductPopup}
          forName={forName}
          selectedProducts={selectedProducts}
          setCustomItems={setCustomItems}
          setSelectedProducts={setSelectedProducts}
          forName1={forName1}
        />
      ) : null}
      <Loader isActive={isLoading} />
      {toast && <CustomToast setToast={setToast} message={message} />}
    </View>
  );
};

export default Editpopup;
