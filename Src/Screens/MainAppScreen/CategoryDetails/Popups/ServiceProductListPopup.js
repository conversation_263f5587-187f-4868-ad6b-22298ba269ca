import {
  Animated,
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../../Designe/designeSheet';

const ServiceProductListPopup = ({
  headerHeight,
  productList,
  setProductPopup,
  forName,
  selectedProducts,
  setCustomItems,
  setSelectedProducts,
  forName1,
}) => {
  return (
    <View
      style={{
        backgroundColor: 'rgba(0,0,0,0.5)',
        position: 'absolute',
        height: '100%',
        width: '100%',
        justifyContent: 'flex-end',
        gap: 20,
        zIndex: 4,
      }}>
      <Animated.View
        style={{
          height: headerHeight,
          width: '100%',
          backgroundColor: 'white',
          borderTopLeftRadius: 40,
          borderTopRightRadius: 40,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Text
            style={{
              fontSize: 16,
              color: '#000000',
              fontFamily: designeSheet.QuicksandBold,
              marginHorizontal: 15,
              marginTop: 20,
              flex: 1,
            }}>
            {'Choose Product For ' + forName1}
          </Text>
          <TouchableOpacity
            onPress={() => {
              Animated.timing(headerHeight, {
                toValue: 0,
                duration: 400,
                useNativeDriver: false,
              }).start(() => {
                setProductPopup(false);
              });
            }}>
            <Image
              source={ImagePath.crossimg}
              style={{
                height: 24,
                width: 24,
                marginTop: 20,
                marginHorizontal: 20,
              }}
            />
          </TouchableOpacity>
        </View>

        <View style={{gap: 5, marginTop: 10}}>
          {productList.map((item, index) => (
            <TouchableOpacity
              // On product name click
              onPress={() => {
                console.log('forName -< ', forName);
                // Update selectedProducts
                setSelectedProducts(prev => ({
                  ...prev,
                  [forName]: index, // this index = product index in list
                }));

                // Update customItems too
                setCustomItems(prev =>
                  prev.map(item =>
                    item.serviceRef === forName.split('-')[0] // use `_id` if available
                      ? {...item, selectedProduct: index}
                      : item,
                  ),
                );
              }}
              style={{
                backgroundColor: '#FFFFFF',
                borderColor: '#CCCCCC',
                padding: 10,
                borderRadius: 8,
                marginHorizontal: 5,
                height: 40,
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Text
                style={{
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                  flex: 1,
                }}>
                {item.name}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginRight: 10,
                }}>
                {`₹${item.price}`}
              </Text>
              <View
                style={{
                  borderWidth: 1,
                  height: 15,
                  width: 15,
                  borderRadius: 10,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                {selectedProducts[forName] === index ? (
                  <View
                    style={{
                      height: 7,
                      width: 7,
                      backgroundColor: 'black',
                      borderRadius: 10,
                    }}
                  />
                ) : null}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </Animated.View>
    </View>
  );
};

export default ServiceProductListPopup;

const styles = StyleSheet.create({});
