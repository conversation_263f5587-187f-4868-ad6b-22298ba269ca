import {Animated, Image, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useRef} from 'react';
import {IMAGE_BASE_URL} from '../../../utils/urls';
import designeSheet from '../../../Designe/designeSheet';
import LinearGradient from 'react-native-linear-gradient';

const StickyCartView = ({cartData, props}) => {
  const bottom = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  function animate() {
    Animated.parallel([
      Animated.timing(bottom, {
        toValue: 50,
        duration: 800,
        useNativeDriver: false,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 2500,
        useNativeDriver: false,
      }),
    ]).start();
  }

  useEffect(() => {
    animate();
  }, []);

  if (!cartData || cartData.length === 0) return null;

  return (
    <Animated.View
      style={{
        position: 'absolute',
        alignSelf: 'center',
        bottom,
        opacity,
        borderRadius: 25,
        overflow: 'hidden',
        elevation: 5,
      }}>
      <TouchableOpacity
        onPress={() => props.navigation.navigate('BeautyCart')}
        activeOpacity={0.8}>
        <LinearGradient
          colors={['#eeeeee', '#cccccc']}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={{
            paddingHorizontal: 8,
            paddingVertical: 8,
            flexDirection: 'row',
            alignItems: 'center',
            borderRadius: 25,
          }}>
          {/* Images */}
          <View style={{flexDirection: 'row', marginRight: 10}}>
            {cartData.slice(0, 3).map((item, index) => (
              <View
                key={index}
                style={{marginLeft: index === 0 ? 0 : -15, zIndex: index}}>
                <Image
                  source={{uri: `${IMAGE_BASE_URL}${item.itemRef.image}`}}
                  style={{
                    height: 30,
                    width: 30,
                    borderRadius: 15,
                    borderWidth: 2,
                    borderColor: 'white',
                  }}
                  resizeMode="cover"
                />
              </View>
            ))}
          </View>

          {/* Text */}
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
            }}>
            {cartData.length > 3
              ? `3+ Items >`
              : `${cartData.length} Item${cartData.length > 1 ? 's' : ''} >`}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default StickyCartView;
