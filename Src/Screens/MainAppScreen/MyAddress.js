import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';

const MyAddress = props => {
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'My Address'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View
        style={{
          borderWidth: 1,
          borderColor: '#CCCCCCCC',
          marginTop: 24,
        }}>
        <View
          style={{
            marginHorizontal: 20,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Image source={ImagePath.cirplus} style={{height: 24, width: 24}} />
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              marginVertical: 15,
            }}>
            {'  Add New Address'}
          </Text>
        </View>
      </View>
      <View style={{marginTop: 24, gap: 18}}>
        {[0, 0].map((item, index) => (
          <View
            key={index}
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCCCC',
            }}>
            <View style={{marginHorizontal: 20}}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <Image
                  source={ImagePath.locationimg}
                  style={{height: 15, width: 12}}
                />
                <Text
                  style={{
                    flex: 1,
                    fontSize: 16,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                    marginVertical: 15,
                  }}>
                  {'  Manoj Kumar'}
                </Text>
                <Image
                  source={ImagePath.doted}
                  style={{height: 16, width: 16}}
                />
              </View>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#6C6C6C',
                }}>
                {
                  'House no - 4567, Near Nirwana Building, New Chandigarh, 160022'
                }
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#6C6C6C',
                  marginBottom: 10,
                }}>
                {'Mob no: '}
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#000000',
                  }}>
                  {'8978979876'}
                </Text>
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default MyAddress;

const styles = StyleSheet.create({});
