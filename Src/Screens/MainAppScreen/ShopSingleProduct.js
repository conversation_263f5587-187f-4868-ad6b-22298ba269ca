import {
  Image,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';
import {singleProductDetails} from "../../Api's/Api";
import Loader from '../../components/Loader';
import {IMAGE_BASE_URL} from '../../utils/urls';
import {calculateDiscountedPrice} from '../../utils/CommonFunctions';

const ShopSingleProduct = ({navigation, route}) => {
  const data = route.params;
  const [isLoading, setIsLoading] = useState(false);
  const [productDetail, setProductDetail] = useState();

  const ratingsData = [
    {stars: 5, percent: 57},
    {stars: 4, percent: 25},
    {stars: 3, percent: 10},
    {stars: 2, percent: 3},
    {stars: 1, percent: 5},
  ];

  const [related, setRelated] = useState([
    {
      Image: ImagePath.singles2,
    },
    {
      Image: ImagePath.singles3,
    },
    {
      Image: ImagePath.singles4,
    },
    {
      Image: ImagePath.singles2,
    },
    {
      Image: ImagePath.singles3,
    },
    {
      Image: ImagePath.singles4,
    },
  ]);

  const [questionTab, setQuestionTab] = useState(false);

  useEffect(() => {
    getProductDetails();
    console.log('cjbscjbjbc', data.slug);
  }, []);

  async function getProductDetails() {
    setIsLoading(true);
    try {
      const result = await singleProductDetails(data.slug);
      if (result.status == 200) {
        setProductDetail(result.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  const uniqueSizes = [];
  const uniqueColors = [];

  productDetail?.variations?.forEach(variation => {
    // Unique Sizes
    if (!uniqueSizes.find(item => item._id === variation.size._id)) {
      uniqueSizes.push(variation.size);
    }

    // Unique Colors
    if (!uniqueColors.find(item => item._id === variation.color._id)) {
      uniqueColors.push(variation.color);
    }
  });

  return productDetail == undefined ? (
    <Loader isActive={isLoading} />
  ) : (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <ScrollView nestedScrollEnabled={true} style={{flex: 1}}>
        <HeaderComp
          title={productDetail.name}
          carttrue={true}
          onpressback={() => {
            navigation.goBack();
          }}
        />
        <Image
          source={{uri: `${IMAGE_BASE_URL}${productDetail.thumbnail}`}}
          style={{height: 365, width: '100%', marginTop: 12}}
        />
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 8,
            marginHorizontal: 15,
            marginTop: 18,
          }}>
          <View style={{marginHorizontal: 10, marginTop: 10}}>
            <Text
              style={{
                fontSize: 15,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {productDetail.name}
            </Text>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandRegular,
                color: '#000000',
              }}>
              {productDetail.description}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: 15,
              }}>
              <View>
                <Text
                  style={{
                    fontSize: 11,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#6C6C6C',
                    textDecorationLine: 'line-through',
                  }}>
                  {'₹' + productDetail.variations[0].price}
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandBold,
                      color: '#000000',
                      textDecorationLine: 'none',
                    }}>
                    {' ₹' +
                      calculateDiscountedPrice(
                        productDetail.variations[0].price,
                        productDetail.variations[0].discount,
                      )}
                  </Text>
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: 'green',
                  }}>
                  {productDetail.variations[0].discount + '% OFF'}
                </Text>
              </View>
              <View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: '#0A985F',
                    alignItems: 'center',
                    justifyContent: 'center',
                    alignSelf: 'flex-end',
                    width: 44,
                    height: 19,
                    borderRadius: 3,
                  }}>
                  <Text
                    style={{
                      fontSize: 10,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#FFFFFF',
                      marginHorizontal: 3,
                    }}>
                    {'4.9'}
                  </Text>
                  <Image
                    source={ImagePath.star}
                    style={{height: 9, width: 9, tintColor: 'white'}}
                  />
                </View>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandRegular,
                    color: '#000000',
                  }}>
                  {'89  Ratings'}
                </Text>
              </View>
            </View>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
                marginTop: 15,
              }}>
              {'Sizes'}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                gap: 10,
                marginTop: 10,
                marginBottom: 15,
              }}>
              {uniqueSizes.map((size, index) => (
                <View
                  key={size._id}
                  style={{
                    borderWidth: 1,
                    borderColor: '#000000',
                    borderRadius: 8,
                    paddingHorizontal: 15,
                    paddingVertical: 8,
                    backgroundColor: '#F5F5F5',
                  }}>
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#000000',
                      fontFamily: designeSheet.QuicksandSemiBold,
                    }}>
                    {size.name}
                  </Text>
                </View>
              ))}
            </View>

            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
                marginTop: 10,
              }}>
              {'Colors'}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                gap: 10,
                marginTop: 10,
                marginBottom: 15,
              }}>
              {uniqueColors.map((color, index) => (
                <View
                  key={color._id}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: '#000000',
                    borderRadius: 20,
                    paddingVertical: 6,
                    paddingHorizontal: 12,
                    backgroundColor: '#F5F5F5',
                  }}>
                  <View
                    style={{
                      height: 14,
                      width: 14,
                      backgroundColor: color.code,
                      borderRadius: 7,
                      marginRight: 8,
                      borderWidth: 1,
                      borderColor: '#999',
                    }}
                  />
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#000000',
                    }}>
                    {color.name}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 8,
            marginHorizontal: 15,
            marginTop: 18,
          }}>
          <View
            style={{
              marginHorizontal: 10,
              marginTop: 10,
              marginBottom: 10,
            }}>
            <Text
              style={{
                fontSize: 14,
                color: '#000000',
                fontFamily: designeSheet.QuicksandSemiBold,
              }}>
              {'Key Product Information'}
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: '#6C6C6C',
                fontFamily: designeSheet.QuicksandSemiBold,
                marginTop: 5,
              }}>
              {productDetail.description}
              <Text
                style={{
                  fontSize: 14,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandSemiBold,
                }}>
                {' read all product details >'}
              </Text>
            </Text>
          </View>
        </View>

        <View style={styles.container}>
          <View style={styles.headerRow}>
            <Text style={styles.header}>Rating & Reviews</Text>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('ReviewScreen');
              }}
              style={styles.rateButton}>
              <Text style={styles.rateText}>Rate Product</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.ratingRow}>
            <Image
              source={ImagePath.greenstar}
              style={{height: 24, width: 25}}
            />
            <Text style={styles.ratingValue}> 4.7/5</Text>
            <View style={styles.ratingMeta}>
              <Text style={styles.metaText}>276 ratings</Text>
              <Text style={styles.metaText}>76 reviews</Text>
            </View>
          </View>
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
            }}>
            {'Customer Reviews'}
          </Text>
          <View style={styles.reviewBars}>
            {ratingsData.map(item => (
              <View key={item.stars} style={styles.reviewRow}>
                <Text style={styles.starText}>{item.stars} Star</Text>
                <View style={styles.barBackground}>
                  <View style={[styles.barFill, {width: `${item.percent}%`}]} />
                </View>
                <Text style={styles.percentText}>{item.percent}%</Text>
              </View>
            ))}
          </View>
        </View>
        <View style={{flexDirection: 'row', justifyContent: 'space-evenly'}}>
          <TouchableOpacity
            onPress={() => {
              setQuestionTab(false);
            }}
            style={{
              backgroundColor: questionTab ? '#FFFFFF' : '#000000',
              borderRadius: 40,
              alignItems: 'center',
              justifyContent: 'center',
              borderWidth: 1,
              borderColor: '#CCCCCC',
            }}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: questionTab ? '#000000' : '#FFFFFF',
                marginHorizontal: 40,
                marginVertical: 10,
              }}>
              {'Reviews(279)'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setQuestionTab(true);
            }}
            style={{
              backgroundColor: questionTab ? '#000000' : '#FFFFFF',

              borderWidth: 1,
              borderColor: '#CCCCCC',
              borderRadius: 40,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: questionTab ? '#FFFFFF' : '#000000',
                marginHorizontal: 40,
                marginVertical: 10,
              }}>
              {'Questions'}
            </Text>
          </TouchableOpacity>
        </View>
        {questionTab ? (
          <>
            {[0, 0, 0].map((item, index) => (
              <View style={{marginHorizontal: 15, marginTop: 18}}>
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                  }}>
                  {'Q: What does this face serum do?'}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#6C6C6C',
                  }}>
                  {
                    'A: It hydrates, brightens, and improves skin texture while reducing fine lines and dark spots over time.'
                  }
                </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={{
                      flex: 1,
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#6C6C6C',
                    }}>
                    {'is this review helpful for you?'}
                  </Text>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Image
                      source={ImagePath.like}
                      style={{height: 20, width: 20}}
                    />
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#6C6C6C',
                      }}>
                      {'  (123) '}
                    </Text>
                    <Image
                      source={ImagePath.dislike}
                      style={{height: 20, width: 20}}
                    />
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#6C6C6C',
                      }}>
                      {'  (7) '}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </>
        ) : (
          <>
            {[0, 0].map((item, index) => (
              <View
                key={index}
                style={{
                  borderWidth: 1,
                  borderColor: '#CCCCCCCC',
                  borderRadius: 8,
                  marginHorizontal: 15,
                  marginTop: 18,
                }}>
                <View
                  style={{
                    marginTop: 10,
                    marginHorizontal: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <View style={{flex: 1}}>
                    <Text
                      style={{
                        fontSize: 14,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#000000',
                      }}>
                      {'Sangetha'}
                    </Text>
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#6C6C6C',
                      }}>
                      {'on 10-05-2025'}
                    </Text>
                  </View>

                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      backgroundColor: '#0A985F',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 2,
                      borderRadius: 3,
                    }}>
                    <Text
                      style={{
                        fontSize: 10,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#FFFFFF',
                        marginHorizontal: 5,
                      }}>
                      {'4.9'}
                    </Text>
                    <Image
                      source={ImagePath.star}
                      style={{height: 9, width: 9, tintColor: 'white'}}
                    />
                  </View>
                </View>
                <View style={{marginTop: 5, marginHorizontal: 10}}>
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#6C6C6C',
                    }}>
                    {
                      'Filled with freshly squeezed Vitamin C, Good Vibes Anti Blemish Glow Toner Vitamin C Toner with power of serum is the Holy Grail for glowing skin. Antioxidant rich Vitamin C brightens the appearance of dull skin and '
                    }
                  </Text>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Text
                      style={{
                        fontFamily: designeSheet.QuicksandMedium,
                        fontSize: 12,
                        color: '#000000',
                      }}>
                      {'Read more'}
                    </Text>
                    <Image
                      source={ImagePath.homeimg1}
                      style={{height: 15, width: 15}}
                    />
                  </View>
                </View>
                <View
                  style={{
                    borderWidth: 1,
                    borderColor: '#CCCCCC',
                    borderStyle: 'dashed',
                    marginHorizontal: 10,
                    marginTop: 10,
                  }}
                />
                <View
                  style={{
                    marginHorizontal: 10,
                    marginTop: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 10,
                  }}>
                  <Text
                    style={{
                      flex: 1,
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#6C6C6C',
                    }}>
                    {'is this review helpful for you?'}
                  </Text>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Image
                      source={ImagePath.like}
                      style={{height: 20, width: 20}}
                    />
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#6C6C6C',
                      }}>
                      {'  (123) '}
                    </Text>
                    <Image
                      source={ImagePath.dislike}
                      style={{height: 20, width: 20}}
                    />
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#6C6C6C',
                      }}>
                      {'  (7) '}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </>
        )}

        <Text
          style={{
            fontSize: 18,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            marginTop: 20,
            alignSelf: 'center',
          }}>
          {'Related Products'}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View
            style={{
              flexDirection: 'row',
              marginHorizontal: 15,
              marginTop: 20,
              gap: 10,
            }}>
            {related.map((item, index) => (
              <View key={index} style={{alignItems: 'center'}}>
                <Image source={item.Image} style={{height: 119, width: 110}} />
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#000000',
                    marginTop: 5,
                  }}>
                  {'Deep Hair Mask'}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                  }}>
                  {'₹899 | 30ml'}
                </Text>
                <View
                  style={{
                    backgroundColor: '#000000',
                    height: 20,
                    width: 110,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: 3,
                    marginTop: 3,
                  }}>
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#FCFCFC',
                    }}>
                    {'Add to cart'}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      </ScrollView>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('Cart');
        }}
        style={{
          backgroundColor: '#000000',
          borderRadius: 8,
          padding: 10,
          alignItems: 'center',
          justifyContent: 'center#000000',
          marginHorizontal: 15,
          marginVertical: 10,
        }}>
        <Text
          style={{
            fontSize: 16,
            color: '#FFFFFF',
            fontFamily: designeSheet.QuicksandMedium,
          }}>
          {'Add To Cart'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default ShopSingleProduct;

const styles = StyleSheet.create({
  container: {
    padding: 15,
    borderRadius: 10,
    backgroundColor: '#fff',
    borderColor: '#ddd',
    borderWidth: 1,
    margin: 15,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  header: {
    fontSize: 14,
    color: '#000000',
    fontFamily: designeSheet.QuicksandSemiBold,
  },
  rateButton: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 5,
  },
  rateText: {
    color: '#1B5ABB',
    fontSize: 13,
    fontFamily: designeSheet.QuicksandSemiBold,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  ratingValue: {
    fontSize: 24,
    fontFamily: designeSheet.QuicksandSemiBold,
    marginRight: 10,
    color: '#000000',
  },
  ratingMeta: {
    marginLeft: 'auto',
    alignItems: 'flex-end',
  },
  metaText: {
    fontSize: 12,
    color: '#6C6C6C',
    fontFamily: designeSheet.QuicksandRegular,
  },
  reviewBars: {
    gap: 6,
    marginTop: 5,
  },
  reviewRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starText: {
    width: 50,
    fontSize: 13,
  },
  barBackground: {
    flex: 1,
    height: 8,
    backgroundColor: '#eee',
    borderRadius: 4,
    overflow: 'hidden',
    marginHorizontal: 8,
  },
  barFill: {
    height: 8,
    backgroundColor: '#00aa55',
  },
  percentText: {
    width: 40,
    fontSize: 13,
    textAlign: 'right',
  },
});
