import {
  Dimensions,
  Image,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import ImagePath from '../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../Designe/designeSheet';
import FaceDetectionScreen from './Steps/FaceDetectionScreen';
import Global from '../../../Globals/Global';

const SkinAnalysis = props => {
  const [faceCount, setFaceCount] = useState(0);

  // Monitor Global.facesLength and update local state
  useEffect(() => {
    const interval = setInterval(() => {
      const currentFaces = Global.facesLength || 0;
      if (currentFaces !== faceCount) {
        console.log('🔥 SkinAnalysis: Updating face count to', currentFaces);
        setFaceCount(currentFaces);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [faceCount]);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <ScrollView style={{}}>
        <View
          style={{
            flexDirection: 'row',
            paddingTop: (StatusBar.currentHeight ?? 0) + 10,
            paddingHorizontal: 20,
            alignItems: 'center',
          }}>
          <TouchableOpacity onPress={() => props.navigation.goBack()}>
            <Image
              style={{
                width: 30,
                resizeMode: 'contain',
                height: 30,
                marginTop: 4,
              }}
              source={require('../../../Assets/Images/backArrow.png')}
            />
          </TouchableOpacity>
          <View style={{alignItems: 'center', flex: 1, marginLeft: -20}}>
            <Text
              style={{fontSize: 24, fontFamily: designeSheet.QuicksandBold}}>
              Derma lens by
            </Text>
            <Image
              style={{
                width: 100,
                resizeMode: 'contain',
                height: 20,
              }}
              source={ImagePath.madamglogo}
            />
          </View>
        </View>
        <Text
          style={{
            fontSize: 15,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#6C6C6C',
            width: '90%',
            textAlign: 'center',
            alignSelf: 'center',
            marginTop: 10,
          }}>
          Follow the basic steps to fill the information so that we can offer
          best skin care routine
        </Text>
        <Image
          style={{
            width: Dimensions.get('screen').width - 60,
            resizeMode: 'contain',
            marginTop: 4,
            alignSelf: 'center',
            marginBottom: 20,
          }}
          source={require('../../../Assets/Images/completionLine.png')}
        />

        {/* <StepOne /> */}
        {/* <StepTwo /> */}
        {/* <StepThree /> */}

        <FaceDetectionScreen />

        {/* <TouchableOpacity
          style={{
            flexDirection: 'row',
            height: 40,
            backgroundColor: 'black',
            borderRadius: 10,
            alignItems: 'center',
            justifyContent: 'center',
            margin: 20,
          }}>
          <Text
            style={{
              fontSize: 15,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: 'white',
            }}>
            Next
          </Text>
          <Image
            style={{
              width: 18,
              height: 10,
              tintColor: 'white',
              transform: [{rotate: '180deg'}],
              marginTop: 2.5,
            }}
            source={require('../../../Assets/Images/backArrow.png')}
          />
        </TouchableOpacity> */}
      </ScrollView>
    </View>
  );
};
const styles = StyleSheet.create({
  label: {
    fontSize: 16,
    fontFamily: designeSheet.QuicksandMedium,
    color: '#000',
    marginTop: 10,
  },
  input: {
    borderColor: '#CCCCCC',
    borderWidth: 1,
    padding: 12,
    borderRadius: 8,
    marginBottom: 5,
    fontSize: 15,
    marginTop: 10,
    fontFamily: designeSheet.QuicksandMedium,
    color: 'black',
  },
  error: {
    fontSize: 12,
    color: 'red',
    marginBottom: 5,
    marginTop: 2,
  },
});

export default SkinAnalysis;
