import {StyleSheet, Text, TextInput, View} from 'react-native';
import React, {useState} from 'react';
import designeSheet from '../../../../Designe/designeSheet';
import Global from '../../../../Globals/Global';

const StepOne = () => {
  const [name, setName] = useState(Global.userData?.name);
  const [number, setNumber] = useState(Global.userData?.mobile);

  return (
    <View style={{marginHorizontal: 20}}>
      <Text
        style={{
          fontSize: 18,
          fontFamily: designeSheet.QuicksandSemiBold,
          color: '#000000',
          width: '90%',
          textAlign: 'center',
          alignSelf: 'center',
          marginTop: 10,
        }}>
        Enter your details to receive your detailed skin analysis reposrt
      </Text>
      <Text style={styles.label}>Enter your name</Text>
      <TextInput
        style={styles.input}
        placeholder="Your Name"
        value={name}
        onChangeText={setName}
        placeholderTextColor="gray"
      />
      <Text style={styles.label}>Enter your mobile number</Text>
      <TextInput
        style={styles.input}
        placeholder="Your Number"
        value={number}
        onChangeText={setNumber}
        placeholderTextColor="gray"
      />
    </View>
  );
};

export default StepOne;

const styles = StyleSheet.create({
  label: {
    fontSize: 16,
    fontFamily: designeSheet.QuicksandMedium,
    color: '#000',
    marginTop: 10,
  },
  input: {
    borderColor: '#CCCCCC',
    borderWidth: 1,
    padding: 12,
    borderRadius: 8,
    marginBottom: 5,
    fontSize: 15,
    marginTop: 10,
    fontFamily: designeSheet.QuicksandMedium,
    color: 'black',
  },
  error: {
    fontSize: 12,
    color: 'red',
    marginBottom: 5,
    marginTop: 2,
  },
});
