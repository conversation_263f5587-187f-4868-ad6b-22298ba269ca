import React, {useEffect, useRef, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import {
  Camera,
  useCameraDevice,
  useFrameProcessor,
} from 'react-native-vision-camera';
import type {
  FaceDetectionOptions,
  scanFaces,
} from 'react-native-vision-camera-face-detector';

const StepThree = () => {
  const [hasPermission, setHasPermission] = useState(false);

  const faceDetectionOptions = useRef<FaceDetectionOptions>({
    // e.g. mode: 'accurate', detectLandmarks: true, runClassifications: true
  });

  const device = useCameraDevice('front');

  useEffect(() => {
    (async () => {
      const status = await Camera.requestCameraPermission();
      setHasPermission(status === 'granted');
    })();
  }, []);

  const frameProcessor = useFrameProcessor(frame => {
    const detectedFaces = scanFaces(frame, faceDetectionOptions.current);
    // ❗ Can't use setFaces directly here — runs in UI thread
    // Just log for now
    console.log(`Faces detected: ${detectedFaces.length}`);
  }, []);

  if (device == null) return <View />;

  return device && hasPermission ? (
    <Camera
      style={StyleSheet.absoluteFill}
      device={device}
      isActive
      // frameProcessor={frameProcessor}
    />
  ) : null;
};

export default StepThree;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: '#000'},
});
