import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';

export default function SimpleFaceDetection() {
  const [faceDetected, setFaceDetected] = useState(false);
  const [counter, setCounter] = useState(0);

  // Simple timer that toggles face detection every 3 seconds
  useEffect(() => {
    const timer = setInterval(() => {
      setFaceDetected(prev => !prev);
      setCounter(prev => prev + 1);
      console.log('Face detection toggled:', !faceDetected);
    }, 3000);

    return () => clearInterval(timer);
  }, [faceDetected]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Simple Face Detection Test</Text>
      
      <View style={styles.statusContainer}>
        <View style={[
          styles.statusDot, 
          faceDetected ? styles.detected : styles.notDetected
        ]} />
        <Text style={styles.statusText}>
          {faceDetected ? 'Face Detected!' : 'No Face Detected'}
        </Text>
      </View>

      <Text style={styles.counter}>Counter: {counter}</Text>

      <TouchableOpacity 
        style={styles.button}
        onPress={() => setFaceDetected(!faceDetected)}
      >
        <Text style={styles.buttonText}>Toggle Manually</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
    padding: 20,
  },
  title: {
    fontSize: 24,
    color: '#fff',
    marginBottom: 40,
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 10,
  },
  detected: {
    backgroundColor: '#00ff88',
  },
  notDetected: {
    backgroundColor: '#ff6b6b',
  },
  statusText: {
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
  },
  counter: {
    fontSize: 16,
    color: '#ccc',
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
