import {
  View,
  Text,
  FlatList,
  Dimensions,
  Image,
  TouchableOpacity,
} from 'react-native';
import React, {useState} from 'react';
import designeSheet from '../../../../Designe/designeSheet';

const StepTwo = () => {
  const data = [
    {
      heading: 'How do you identify yourself?',
      subData: [
        {title: 'Male', image: require('../../../../Assets/Images/male.png')},
        {
          title: 'Female',
          image: require('../../../../Assets/Images/female.png'),
        },
      ],
    },
    {
      heading: 'What type of skin do you have?',
      subData: [
        {title: 'Oily', image: require('../../../../Assets/Images/oily.png')},
        {
          title: 'Sensitive',
          image: require('../../../../Assets/Images/sensitive.png'),
        },
        {
          title: 'Combination',
          image: require('../../../../Assets/Images/combination.png'),
        },
        {title: 'Dry', image: require('../../../../Assets/Images/dry.png')},
        {
          title: 'Normal',
          image: require('../../../../Assets/Images/normal.png'),
        },
      ],
    },
    {
      heading: 'Choose your skin type of concern',
      subData: [
        {
          title: 'Acne skin',
          image: require('../../../../Assets/Images/acneskin.png'),
        },
        {
          title: 'Aging ',
          image: require('../../../../Assets/Images/aging.png'),
        },
        {
          title: 'Pigmentation',
          image: require('../../../../Assets/Images/pigmentation.png'),
        },
        {
          title: 'Dehydrated skin',
          image: require('../../../../Assets/Images/dehydratedskin.png'),
        },
        {
          title: 'Reactive skin',
          image: require('../../../../Assets/Images/reactiveskin.png'),
        },
      ],
    },
  ];

  // Maintain selected option index per section
  const [selectedIndexes, setSelectedIndexes] = useState<{
    [key: number]: number;
  }>({});

  const handleSelect = (sectionIndex: number, itemIndex: number) => {
    setSelectedIndexes(prev => ({
      ...prev,
      [sectionIndex]: itemIndex,
    }));
  };

  return (
    <View>
      {data.map((section, sectionIndex) => (
        <View key={sectionIndex}>
          <Text
            style={{
              fontSize: 18,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              width: '90%',
              textAlign: 'center',
              alignSelf: 'center',
              marginVertical: 10,
            }}>
            {section.heading}
          </Text>

          <FlatList
            data={section.subData}
            keyExtractor={(_, i) => i.toString()}
            numColumns={sectionIndex === 0 ? 2 : 3}
            contentContainerStyle={{alignItems: 'center'}}
            renderItem={({item, index}) => {
              const isSelected = selectedIndexes[sectionIndex] === index;
              return (
                <TouchableOpacity
                  onPress={() => handleSelect(sectionIndex, index)}
                  activeOpacity={0.7}
                  style={{
                    width:
                      sectionIndex === 0
                        ? Dimensions.get('screen').width / 2
                        : Dimensions.get('screen').width / 3,
                    alignItems: 'center',
                    marginBottom: 20,
                  }}>
                  <View
                    style={{
                      height: 100,
                      width: 94,
                      borderWidth: 1.5,
                      borderColor: isSelected ? '#990057' : '#ccc',
                      justifyContent: 'center',
                      borderRadius: 10,
                      backgroundColor: isSelected ? '#FCE6F2' : '#fff',
                    }}>
                    <Image
                      style={{
                        width: 40,
                        resizeMode: 'contain',
                        alignSelf: 'center',
                        height: 40,
                      }}
                      source={item.image}
                    />
                    <Text
                      style={{
                        textAlign: 'center',
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandBold,
                        color: 'black',
                        marginTop: 8,
                      }}>
                      {item.title}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            }}
          />
        </View>
      ))}
    </View>
  );
};

export default StepTwo;
