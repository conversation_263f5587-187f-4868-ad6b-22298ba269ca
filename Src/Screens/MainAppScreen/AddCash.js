import {
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const AddCash = props => {
  const [cash, setCash] = useState('');
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Add Cash'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View style={{flex: 1}}>
        <Text
          style={{
            fontSize: 12,
            fontFamily: designeSheet.QuicksandMedium,
            color: '#6C6C6C',
            marginTop: 21,
            alignSelf: 'center',
          }}>
          {'Enter Amount'}
        </Text>
        <View
          style={{
            borderColor: '#000000',
            borderBottomWidth: 1,
            marginTop: 12,
            marginHorizontal: 36,
            backgroundColor: '#F0F0F0',
          }}>
          <TextInput
            placeholder="₹1,000"
            style={{
              alignSelf: 'center',
              fontSize: 28,
              fontFamily: designeSheet.QuicksandMedium,
            }}
            placeholderTextColor={'#212529'}
            secureTextEntry
            value={cash}
            onChangeText={val => setCash(val)}
          />
        </View>
        <View
          style={{
            flexDirection: 'row',
            marginTop: 12,
            alignSelf: 'center',
            gap: 12,
          }}>
          {[0, 0, 0].map((item, index) => (
            <View
            key={index}
              style={{
                borderWidth: 1,
                borderColor: '#615657',
                borderRadius: 8,
                backgroundColor: '#FEFEFE',
              }}>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#939393',
                  marginHorizontal: 24,
                  marginVertical: 10,
                }}>
                {'₹200'}
              </Text>
            </View>
          ))}
        </View>
        <View
          style={{
            backgroundColor: '#F0F0F0',
            marginTop: 20,
            alignSelf: 'center',
            borderRadius: 4,
            padding: 5,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignSelf: 'center',
              marginVertical: 6,
              marginHorizontal: 10,
            }}>
            {[0, 0, 0].map((item, index) => (
              <View key={index} style={{marginLeft: -6}}>
                <Image source={ImagePath.ac1} style={{height: 19, width: 19}} />
              </View>
            ))}
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandRegular,
                color: '#6E707A',
              }}>
              {'  578 People'}

              <Text
                style={{
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#0A985F',
                }}>
                {'  added cash today.'}
              </Text>
            </Text>
          </View>
        </View>
      </View>

      <View style={{flex: 1}}>
        <View style={{flexDirection: 'row', gap: 6, alignSelf: 'center'}}>
          <Image source={ImagePath.ac4} style={{height: 16, width: 16}} />
          <Text
            style={{
              fontSize: 12,
              fontFamily: designeSheet.QuicksandRegular,
              color: '#6C757D',
            }}>
            {'100% safe & secure'}
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            props.navigation.goBack();
          }}
          style={{
            backgroundColor: '#000000',
            borderRadius: 100,
            marginHorizontal: 20,
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 20,
          }}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#FFFFFF',
              marginVertical: 12,
            }}>
            {'Add Cash'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AddCash;

const styles = StyleSheet.create({});
