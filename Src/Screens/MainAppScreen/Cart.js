import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';

const Cart = props => {
  const [lastMinone, setLastMinone] = useState([
    {
      image: ImagePath.c2,
    },
    {
      image: ImagePath.c3,
    },
    {
      image: ImagePath.c2,
    },
    {
      image: ImagePath.c3,
    },
    {
      image: ImagePath.c2,
    },
    {
      image: ImagePath.c3,
    },
  ]);
  const [lastMin, setLastMin] = useState([
    {
      image: ImagePath.c4,
    },
    {
      image: ImagePath.c5,
    },
    {
      image: ImagePath.c6,
    },
    {
      image: ImagePath.c4,
    },
    {
      image: ImagePath.c5,
    },
    {
      image: ImagePath.c6,
    },
  ]);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'My Cart'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <SafeAreaView style={{flex: 1, backgroundColor: '#FFF'}}>
        <ScrollView nestedScrollEnabled={true} style={{flex: 1}}>
          <View
            style={{
              flex: 1,
              borderWidth: 1,
              borderColor: '#CCCCCC',
              borderRadius: 12,
              marginHorizontal: 15,
              marginTop: 24,
            }}>
            <View
              style={{
                flexDirection: 'row',
                marginHorizontal: 11,
                marginVertical: 10,
              }}>
              <Image source={ImagePath.c1} style={{height: 100, width: 103}} />
              <View style={{flex: 1, marginHorizontal: 20}}>
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                  }}>
                  {'MAERODOC'}
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandRegular,
                      color: '#000000',
                    }}>
                    {' by madam g 105 Face serum (50 ml)..'}
                  </Text>
                </Text>
                <Text
                  style={{
                    fontSize: 13,
                    fontFamily: designeSheet.QuicksandRegular,
                    color: '#6C6C6C',
                    textDecorationLine: 'line-through',
                    marginTop: 6,
                  }}>
                  {'₹499'}
                  <Text
                    style={{
                      fontSize: 16,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                      textDecorationLine: '',
                    }}>
                    {'  ₹499'}
                    <Text
                      style={{
                        fontSize: 13,
                        fontFamily: designeSheet.QuicksandRegular,
                        color: '#6C6C6C',
                      }}>
                      {'   |'}
                      <Text
                        style={{
                          fontSize: 14,
                          color: '#2CAA3B',
                          fontFamily: designeSheet.QuicksandMedium,
                        }}>
                        {'   50% off'}
                      </Text>
                    </Text>
                  </Text>
                </Text>
                <View
                  style={{
                    borderWidth: 0.5,
                    borderColor: '#000000',
                    borderRadius: 4,
                    height: 23,
                    width: 86,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 15,
                  }}>
                  <Text>{'-   1   +'}</Text>
                </View>
              </View>
            </View>
          </View>
          <View style={{backgroundColor: '#FBE6FF', marginTop: 30}}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
                marginHorizontal: 15,
                marginTop: 10,
              }}>
              {'Last minute Add-on'}
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={{flexDirection: 'row', marginBottom: 10}}>
                {lastMinone.map((item, Index) => (
                  <View
                    key={Index}
                    style={{
                      marginTop: 8,
                      marginHorizontal: 15,
                      borderWidth: 1,
                      borderColor: '#CCCCCC',
                      borderRadius: 8,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Image
                      source={item.image}
                      style={{height: 104, width: 93}}
                    />

                    <View style={{marginHorizontal: 10}}>
                      <Text
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandSemiBold,
                          color: '#000000',
                        }}>
                        {'Combo Of Face seru...'}
                      </Text>
                      <Text
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandRegular,
                          color: '#6C6C6C',
                          textDecorationLine: 'line-through',
                          marginTop: 6,
                        }}>
                        {'₹499'}
                        <Text
                          style={{
                            fontSize: 16,
                            fontFamily: designeSheet.QuicksandSemiBold,
                            color: '#000000',
                            textDecorationLine: '',
                          }}>
                          {'  ₹499'}
                          <Text
                            style={{
                              fontSize: 13,
                              fontFamily: designeSheet.QuicksandRegular,
                              color: '#6C6C6C',
                            }}>
                            {'   |'}
                            <Text
                              style={{
                                fontSize: 14,
                                color: '#2CAA3B',
                                fontFamily: designeSheet.QuicksandMedium,
                              }}>
                              {'   50% off'}
                            </Text>
                          </Text>
                        </Text>
                      </Text>
                      <TouchableOpacity
                        style={{
                          backgroundColor: '#000000',
                          height: 24,
                          width: 60,
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: 4,
                          marginTop: 10,
                        }}>
                        <Text
                          style={{
                            fontSize: 12,
                            color: '#FFFFFF',
                            fontFamily: designeSheet.QuicksandSemiBold,
                          }}>
                          {'Add'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            </ScrollView>
          </View>
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              marginHorizontal: 15,
              marginTop: 30,
            }}>
            {'Featured Deals'}
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginHorizontal: 15,
                marginTop: 9,
                gap: 15,
              }}>
              {lastMin.map((item, Index) => (
                <View key={Index}>
                  <Image
                    source={item.image}
                    style={{height: 117, width: 125, borderRadius: 8}}
                  />
                  <Text
                    style={{
                      width: 100,
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#000000',
                    }}>
                    {'Face Night Cream vitamin c cream'}
                  </Text>
                  <Text
                    style={{
                      fontSize: 13,
                      fontFamily: designeSheet.QuicksandRegular,
                      color: '#6C6C6C',
                      textDecorationLine: 'line-through',
                      marginTop: 6,
                    }}>
                    {'₹499'}
                    <Text
                      style={{
                        fontSize: 16,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#000000',
                        textDecorationLine: '',
                      }}>
                      {'  ₹499'}
                      <Text
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandRegular,
                          color: '#6C6C6C',
                        }}>
                        {'   |'}
                        <Text
                          style={{
                            fontSize: 14,
                            color: '#2CAA3B',
                            fontFamily: designeSheet.QuicksandMedium,
                          }}>
                          {'   50% off'}
                        </Text>
                      </Text>
                    </Text>
                  </Text>

                  <TouchableOpacity
                    style={{
                      backgroundColor: '#000000',
                      height: 24,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: 4,
                      marginTop: 10,
                    }}>
                    <Text
                      style={{
                        fontSize: 12,
                        color: '#FFFFFF',
                        fontFamily: designeSheet.QuicksandSemiBold,
                      }}>
                      {'Add'}
                    </Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </ScrollView>
          <View
            style={{borderWidth: 1, borderColor: '#E6E6E6', marginTop: 30}}
          />

          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              marginHorizontal: 15,
              marginTop: 5,
            }}>
            {'Payment Summary'}
          </Text>
          <View
            style={{borderWidth: 1, borderColor: '#E6E6E6', marginTop: 10}}
          />
          {[0, 0, 0, 0].map((item, Index) => (
            <View
              key={Index}
              style={{
                marginHorizontal: 15,
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginVertical: 8,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {'Total MRP'}
              </Text>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {'₹498'}
              </Text>
            </View>
          ))}
          <View
            style={{
              borderWidth: 0.8,
              borderColor: '#6C6C6CB2',
              marginTop: 10,
              marginHorizontal: 15,
            }}
          />
          <View
            style={{
              marginHorizontal: 15,
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginVertical: 8,
            }}>
            <Text
              style={{
                fontSize: 15,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#000000',
              }}>
              {'Amount to pay'}
            </Text>
            <Text
              style={{
                fontSize: 16,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#000000',
              }}>
              {'₹478'}
            </Text>
          </View>
          <TouchableOpacity
            style={{
              backgroundColor: '#000000',
              borderRadius: 8,
              padding: 10,
              alignItems: 'center',
              justifyContent: 'center#000000',
              marginHorizontal: 15,
              marginTop: 20,
              marginBottom: 20,
            }}>
            <Text
              style={{
                fontSize: 16,
                color: '#FFFFFF',
                fontFamily: designeSheet.QuicksandMedium,
              }}>
              {'Pay ₹478'}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default Cart;

const styles = StyleSheet.create({});
