import {
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const Withdraw = props => {
  const [cash, setCash] = useState('');
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Withdraw Cash'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View style={{flex: 1}}>
        <View
          style={{
            backgroundColor: '#F0F0F0',
            borderRadius: 100,
            alignSelf: 'center',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 29,
          }}>
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandMedium,
              color: '#343A40',
              marginHorizontal: 20,
              marginVertical: 10,
            }}>
            {'Available for Withdrawal:'}
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#000000',
                marginTop: 21,
                alignSelf: 'center',
              }}>
              {'  ₹10,000'}
            </Text>
          </Text>
        </View>

        <View
          style={{
            borderColor: '#000000',
            borderBottomWidth: 1,
            marginTop: 12,
            marginHorizontal: 36,
            backgroundColor: '#F0F0F0',
          }}>
          <TextInput
            placeholder="₹1,000"
            style={{
              alignSelf: 'center',
              fontSize: 28,
              fontFamily: designeSheet.QuicksandMedium,
            }}
            placeholderTextColor={'#212529'}
            secureTextEntry
            value={cash}
            onChangeText={val => setCash(val)}
          />
        </View>
        <Text
          style={{
            fontSize: 12,
            fontFamily: designeSheet.QuicksandMedium,
            color: '#6C757D',
            marginTop: 12,
            alignSelf: 'center',
          }}>
          {'Min ₹100 - Max ₹10,00000'}
        </Text>
      </View>

      <View style={{flex: 1}}>
        <View style={{flexDirection: 'row', gap: 6, alignSelf: 'center'}}>
          <Image source={ImagePath.ac4} style={{height: 16, width: 16}} />
          <Text
            style={{
              fontSize: 12,
              fontFamily: designeSheet.QuicksandRegular,
              color: '#6C757D',
            }}>
            {'100% safe & secure'}
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            props.navigation.goBack();
          }}
          style={{
            backgroundColor: '#000000',
            borderRadius: 100,
            marginHorizontal: 20,
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 20,
          }}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#FFFFFF',
              marginVertical: 12,
            }}>
            {'Add Cash'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Withdraw;

const styles = StyleSheet.create({});
