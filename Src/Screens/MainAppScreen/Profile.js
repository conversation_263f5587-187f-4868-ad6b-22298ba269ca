import {
  Alert,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';
import {setData} from '../../Redux/CreatSlice';
import {useDispatch} from 'react-redux';
import Global from '../../Globals/Global';
import {IMAGE_BASE_URL} from '../../utils/urls';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Profile = props => {
  const [info, setInfo] = useState([
    {
      img: ImagePath.service,
      title: 'Bookings',
    },
    {
      img: ImagePath.orderimg,
      title: 'Orders',
    },
    {
      img: ImagePath.ragister,
      title: 'Register',
    },
  ]);
  const [sections, setSections] = useState([
    {
      imagei: ImagePath.profilelocation,
      textdata: 'Address',
    },
    {
      imagei: ImagePath.profileabout,
      textdata: 'About us',
    },
    {
      imagei: ImagePath.profilecontant,
      textdata: 'Contact Us',
    },
    {
      imagei: ImagePath.profilenoti,
      textdata: 'Notification preference',
    },
    {
      imagei: ImagePath.profileprivacy,
      textdata: 'Privacy Policy',
    },
    {
      imagei: ImagePath.profilehelp,
      textdata: 'Help & Support',
    },
  ]);
  const dispatch = useDispatch();

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <TouchableOpacity
        onPress={() => {
          props.navigation.navigate('EditProfile');
        }}
        style={{
          marginTop: Platform.OS == 'android' ? 30 : 50,
          marginHorizontal: 20,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <Image
          source={{uri: `${IMAGE_BASE_URL}${Global.userData.image}`}}
          style={{
            height: 54,
            width: 54,
          }}
        />
        <View
          style={{
            flex: 1,
            marginHorizontal: 10,
          }}>
          <Text
            style={{
              fontSize: 19,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#1C1C28',
            }}>
            {Global.userData.name}
          </Text>
          <Text
            style={{
              fontSize: 12,
              fontFamily: designeSheet.QuicksandRegular,
              color: '#6C6C6C',
            }}>
            {`+91 ${Global.userData.mobile}`}
          </Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
            }}>
            {'Edit'}
          </Text>
          <Image
            source={ImagePath.homeimg4}
            style={{
              height: 18,
              width: 18,
              tintColor: '#000000',
            }}
          />
        </View>
      </TouchableOpacity>
      <View
        style={{
          borderWidth: 0.5,
          borderColor: '#DEDEDE',
          marginTop: 15,
        }}
      />
      <TouchableOpacity
        onPress={() => {
          props.navigation.navigate('Wallet');
        }}
        style={{
          borderWidth: 1,
          borderColor: '#CCCCCCCC',
          borderRadius: 8,
          marginHorizontal: 20,
          marginTop: 20,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginHorizontal: 10,
            marginVertical: 10,
          }}>
          <Image
            source={ImagePath.wallet}
            style={{
              height: 25,
              width: 24,
            }}
          />
          <Text
            style={{
              flex: 1,
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              marginHorizontal: 5,
            }}>
            {'Wallet: 1,235.05'}
          </Text>
          <Image
            source={ImagePath.homeimg4}
            style={{
              height: 18,
              width: 18,
              tintColor: '#000000',
            }}
          />
        </View>
      </TouchableOpacity>
      <View
        style={{
          flexDirection: 'row',
          marginTop: 20,
          gap: 15,
          alignSelf: 'center',
        }}>
        {info.map((item, index) => (
          <TouchableOpacity
            onPress={() => {
              item.title == 'Orders'
                ? props.navigation.navigate('MyOrder')
                : item.title == 'Register'
                ? props.navigation.navigate('Register')
                : Alert.alert('In Process');
            }}
            key={index}
            style={{
              alignItems: 'center',
            }}>
            <View
              style={{
                borderRadius: 12,
                borderWidth: 1,
                borderColor: '#CCCCCC',
                padding: 20,
                alignItems: 'center',
                gap: 5,
              }}>
              <Image
                source={item.img}
                style={{
                  height: 24,
                  width: 24,
                }}
              />
              <Text
                style={{
                  fontSize: 14,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginHorizontal: index == 1 ? 8 : 0,
                }}>
                {item.title}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
      <View
        style={{
          borderWidth: 1,
          borderColor: '#CCCCCC',
          borderRadius: 8,
          marginHorizontal: 20,
          marginTop: 20,
        }}>
        {sections.map((item, index) => (
          <TouchableOpacity
            onPress={() => {
              item.textdata == 'Address'
                ? props.navigation.navigate('MyAddress')
                : item.textdata == 'Notification preference'
                ? props.navigation.navigate('Notifications')
                : item.textdata == 'Contact Us'
                ? props.navigation.navigate('ContactUs')
                : item.textdata == 'Privacy Policy'
                ? props.navigation.navigate('PrivacyPolicy')
                : item.textdata == 'Help & Support'
                ? props.navigation.navigate('HelpSupport')
                : item.textdata == 'About us'
                ? props.navigation.navigate('AboutUs')
                : null;
            }}
            key={index}
            style={{marginTop: 10}}>
            <View
              style={{
                flexDirection: 'row',
                marginHorizontal: 20,
                alignItems: 'center',
                marginBottom: index == 5 ? 10 : 0,
              }}>
              <Image
                source={item.imagei}
                style={{
                  height: 16,
                  width: 16,
                }}
              />
              <Text
                style={{
                  flex: 1,
                  fontSize: 14,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandMedium,
                  marginHorizontal: 5,
                }}>
                {item.textdata}
              </Text>
              <Image
                source={ImagePath.homeimg4}
                style={{
                  height: 18,
                  width: 18,
                  tintColor: '#000000',
                }}
              />
            </View>
            {index == 5 ? null : (
              <View
                style={{
                  borderWidth: 0.5,
                  borderColor: '#CCCCCCE5',
                  marginHorizontal: 20,
                  marginVertical: 10,
                  marginTop: 20,
                }}
              />
            )}
          </TouchableOpacity>
        ))}
      </View>
      <TouchableOpacity
        onPress={() => {
          Alert.alert('Are You Sure You Want To Logout?', '', [
            {
              text: 'OK',
              onPress: () => {
                dispatch(setData('0'));
                AsyncStorage.clear();
              },
            },
          ]);
        }}
        style={{
          borderWidth: 1,
          borderColor: '#CCCCCCCC',
          borderRadius: 8,
          marginTop: 20,
          marginHorizontal: 20,
          flexDirection: 'row',
          alignItems: 'center',
          padding: 10,
        }}>
        <Image
          source={ImagePath.logoutimg}
          style={{
            height: 26,
            width: 26,
            marginHorizontal: 5,
          }}
        />
        <Text
          style={{
            color: '#FF0000',
            fontSize: 16,
            fontFamily: designeSheet.QuicksandMedium,
          }}>
          {'Logout'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default Profile;

const styles = StyleSheet.create({});
