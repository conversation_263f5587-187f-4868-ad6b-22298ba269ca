import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';

const MyOrderDetail = props => {
  const [priceDetail, setPriceDetail] = useState([
    {
      text1: 'Total MRP',
      text2: '₹998',
    },
    {
      text1: 'Saving on MRP',
      text2: '₹60',
    },
    {
      text1: 'Sub Total',
      text2: '₹938',
    },
    {
      text1: 'Shipping Charges',
      text2: '₹50',
    },
    {
      text1: 'Payment Method',
      text2: 'PREPAID',
    },
    {
      text1: 'Payment Status',
      text2: 'Paid',
    },
    {
      text1: 'Order Total',
      text2: '₹1158',
    },
  ]);
  const [selectedTab, setSelectedTab] = useState('Buy Again');

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#FFF7FB',
      }}>
      <HeaderComp
        title={'Order Details'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <ScrollView>
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCCCC',
            marginTop: 24,
            flexDirection: 'row',
          }}>
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#6C6C6C',
              marginHorizontal: 20,
              marginVertical: 15,
              flex: 1,
            }}>
            {'Order ID: 89FD98D8'}
          </Text>
          <View
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCCCC',
            }}
          />
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#6C6C6C',
              marginHorizontal: 20,
              marginVertical: 15,
              flex: 1,
            }}>
            {'Courier: Ecom Express'}
          </Text>
        </View>

        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            marginHorizontal: 20,
            marginTop: 15,
          }}>
          {'My Shipments (1)'}
        </Text>

        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 8,
            marginHorizontal: 20,
            backgroundColor: '#F0F0F0',
            marginTop: 10,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 10,
              marginVertical: 10,
            }}>
            <Image
              source={ImagePath.ordres}
              style={{
                width: 17,
                height: 17,
              }}
            />
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#6C6C6C',
              }}>
              {'  Order Delivered on 25-06-2025'}
            </Text>
          </View>
          <View
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCC',
            }}
          />
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 5,
              marginHorizontal: 5,
              marginBottom: 6,
            }}>
            <Image
              source={ImagePath.fc}
              style={{
                height: 95,
                width: 98,
              }}
            />
            <View
              style={{
                marginHorizontal: 14,
                flex: 1,
              }}>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#1C1C28',
                }}>
                {'Vitamin C Face Serum, orange extract, alevorea gell'}
              </Text>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#000000',
                }}>
                {'₹835'}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandRegular,
                  color: '#0A985F',
                }}>
                {'Save Rs.54 (14% off)'}
              </Text>
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 10,
              marginBottom: 10,
              gap: 10,
              alignSelf: 'center',
            }}>
            <TouchableOpacity
              onPress={() => setSelectedTab('Buy Again')}
              style={{
                backgroundColor:
                  selectedTab === 'Buy Again' ? '#000000' : '#FFFFFF',
                borderRadius: 40,
                borderWidth: selectedTab === 'Buy Again' ? 0 : 1,
                borderColor: '#CCCCCC',
                // width: 161,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  fontSize: 14,
                  color: selectedTab === 'Buy Again' ? '#FFFFFF' : '#000000',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginVertical: 10,
                  paddingHorizontal: 35,
                }}>
                {'Buy Again'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setSelectedTab('Write Review')}
              style={{
                backgroundColor:
                  selectedTab === 'Write Review' ? '#000000' : '#FFFFFF',
                borderRadius: 40,
                borderWidth: selectedTab === 'Write Review' ? 0 : 1,
                borderColor: '#CCCCCC',
                // width: 161,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  fontSize: 14,
                  color: selectedTab === 'Write Review' ? '#FFFFFF' : '#000000',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginVertical: 10,
                  paddingHorizontal: 35,
                }}>
                {'Write Review'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCCCC',
            borderRadius: 16,
            marginHorizontal: 20,
            marginTop: 15,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              padding: 10,
            }}>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Image
                source={ImagePath.truck}
                style={{
                  height: 24,
                  width: 24,
                }}
              />
              <View>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                  }}>
                  {'  Delivered'}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandRegular,
                    color: '#000000',
                  }}>
                  {'   Delivered at your gate'}
                </Text>
              </View>
            </View>
            <Image
              source={ImagePath.homeimg4}
              style={{
                width: 16,
                height: 16,
                tintColor: '#000000',
              }}
            />
          </View>
        </View>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            marginHorizontal: 20,
            marginTop: 15,
          }}>
          {'Shipping Address'}
        </Text>
        <View
          style={{
            borderWidth: 0.6,
            borderColor: '#CCCCCC',
            marginTop: 10,
          }}
        />
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            marginHorizontal: 20,
            marginTop: 11,
          }}>
          {'Sunil Thakur'}
        </Text>
        <Text
          style={{
            fontSize: 14,
            fontFamily: designeSheet.QuicksandRegular,
            color: '#000000',
            marginHorizontal: 20,
            marginTop: 4,
          }}>
          {'Near sanik Bhawan House no 675, Sector 21C Changigarh'}
        </Text>
        <Text
          style={{
            fontSize: 14,
            fontFamily: designeSheet.QuicksandMedium,
            color: '#000000',
            marginHorizontal: 20,
            marginTop: 4,
          }}>
          {'Mob No: 7879888789'}
        </Text>
        <View
          style={{
            borderWidth: 0.6,
            borderColor: '#CCCCCC',
            marginTop: 10,
          }}
        />
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            marginHorizontal: 20,
            marginTop: 18,
          }}>
          {'Price Details'}
        </Text>
        <View
          style={{
            borderWidth: 1,
            borderColor: '#E6E6E6',
            marginTop: 10,
            marginHorizontal: 20,
          }}
        />
        <View
          style={{
            marginTop: 10,
            gap: 13,
            marginBottom: 20,
          }}>
          {priceDetail.map((item, index) => (
            <View
              style={{
                marginHorizontal: 20,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
              key={index}>
              <Text
                style={{
                  fontSize: index == 6 ? 16 : 15,
                  fontFamily:
                    index == 6
                      ? designeSheet.QuicksandSemiBold
                      : designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {item.text1}
              </Text>
              <Text
                style={{
                  fontSize: index == 6 ? 16 : 15,
                  fontFamily:
                    index == 6
                      ? designeSheet.QuicksandSemiBold
                      : designeSheet.QuicksandMedium,
                  color: index == 1 ? '#0A985F' : '#000000',
                }}>
                {item.text2}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default MyOrderDetail;

const styles = StyleSheet.create({});
