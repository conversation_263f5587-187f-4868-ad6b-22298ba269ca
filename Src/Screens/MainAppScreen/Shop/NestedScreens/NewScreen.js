import {Animated, Text, View, Image, Dimensions} from 'react-native';
import React, {useEffect, useRef} from 'react';

const {width} = Dimensions.get('window');

const NewScreen = () => {
  const vectorTopAnim = useRef(new Animated.Value(-width)).current;
  const vectorBottomAnim = useRef(new Animated.Value(width)).current;
  const textFadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1.2)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(vectorTopAnim, {
        toValue: 0,
        duration: 1500,
        useNativeDriver: true,
      }),
      Animated.timing(vectorBottomAnim, {
        toValue: 0,
        duration: 1500,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]).start(() => {
      Animated.timing(textFadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }).start();
    });
  }, []);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      {/* TOP vector image */}
      {/* <Animated.Image
        source={require('./Vector.png')}
        style={{
          position: 'absolute',
          top: '38%',
          height: 100,
          resizeMode: 'contain',
          transform: [{translateX: vectorTopAnim}],
          zIndex: 2,
        }}
      /> */}

      {/* Center Group image with zoom-out effect */}
      {/* <Animated.Image
        source={require('./Group1.png')}
        style={{
          height: 150,
          width: 150,
          zIndex: 1,
          position: 'absolute',
          transform: [{scale: scaleAnim}],
        }}
      /> */}

      {/* BOTTOM vector image */}
      {/* <Animated.Image
        source={require('./Vector1.png')}
        style={{
          position: 'absolute',
          top: '50%',
          height: 100,
          resizeMode: 'contain',
          transform: [{translateX: vectorBottomAnim}],
          zIndex: 1,
        }}
      /> */}

      {/* Title */}
      <Animated.Text
        style={{
          marginTop: 30,
          fontSize: 50,
          color: 'purple',
          opacity: textFadeAnim,
          position: 'absolute',
          bottom: '30%',
        }}></Animated.Text>

      {/* Subtitle */}
      <Animated.Text
        style={{
          fontSize: 15,
          color: 'purple',
          opacity: textFadeAnim,
          position: 'absolute',
          bottom: '29%',
        }}></Animated.Text>
    </View>
  );
};

export default NewScreen;
