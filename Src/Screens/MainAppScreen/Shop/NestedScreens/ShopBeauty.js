import {
  ActivityIndicator,
  Animated,
  FlatList,
  Image,
  ImageBackground,
  Platform,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../../Designe/designeSheet';
import {animateText} from '../../../../utils/CommonFunctions';
import {shopSubCategories} from "../../../../Api's/Api";
import DynamicImage from '../../../../components/DynamicImage';
import {IMAGE_BASE_URL} from '../../../../utils/urls';
import LinearGradient from 'react-native-linear-gradient';
import Loader from '../../../../components/Loader';
import Global from '../../../../Globals/Global';

const ShopBeauty = props => {
  const {slug} = props.route.params;

  const keywords = ['scrub', 'massage', 'facial', 'therapy'];
  const [index, setIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;
  const scrollY = useRef(new Animated.Value(0)).current;
  const [isLoading, setIsLoading] = useState(false);
  const [subCat, setSubCat] = useState([]);

  useEffect(() => {
    getSubCategories();
    const interval = setInterval(() => {
      animateText(fadeAnim, translateYAnim, keywords, setIndex, 'after');
    }, 2500);
    return () => clearInterval(interval);
  }, []);

  async function getSubCategories() {
    setIsLoading(true);
    try {
      const result = await shopSubCategories(slug);
      if (result.status == 200) {
        setSubCat(result.data.result);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  const headerTranslateY = scrollY.interpolate({
    inputRange: [0, 150],
    outputRange: [0, -100],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  const stickyHeaderOpacity = scrollY.interpolate({
    inputRange: [100, 150],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const stickyHeaderTranslateY = scrollY.interpolate({
    inputRange: [100, 150],
    outputRange: [50, 0],
    extrapolate: 'clamp',
  });

  const animatedTextScale = scrollY.interpolate({
    inputRange: [0, 150],
    outputRange: [1, 1],
    extrapolate: 'clamp',
  });

  const animatedTextColor = scrollY.interpolate({
    inputRange: [0, 150],
    outputRange: ['rgb(108,108,108)', 'rgb(179,60,166)'],
    extrapolate: 'clamp',
  });

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <Loader isActive={isLoading} />

      <Animated.View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 40,
          left: 0,
          right: 0,
          backgroundColor: '#FFF7FB',
          zIndex: 100,
          paddingHorizontal: 20,
          paddingBottom: 10,
          opacity: stickyHeaderOpacity,
          transform: [{translateY: stickyHeaderTranslateY}],
          borderBottomWidth: 0.7,
          borderColor: 'grey',
        }}>
        <View
          style={{
            ...styles.addressView,
            marginHorizontal: 0,
          }}>
          <Image source={ImagePath.locationimg} style={styles.locationIcon} />
          <View style={{flex: 1}}>
            {Global.gettingLocation ? (
              <ActivityIndicator
                style={{
                  alignSelf: 'flex-start',
                  marginVertical: 10,
                  marginLeft: 10,
                }}
                size={15}
                color={'black'}
              />
            ) : (
              <View>
                <View style={styles.addressTextView}>
                  <Text style={styles.addressMainText}>
                    {Global.shortAddress}
                  </Text>
                  <Image style={{marginLeft: 5}} source={ImagePath.ArrowDown} />
                </View>
                <Text
                  numberOfLines={1}
                  style={{...styles.addressText, width: '80%'}}>
                  {Global.fullAddress}
                </Text>
              </View>
            )}
          </View>
          <TouchableOpacity
            onPress={() => {
              props.navigation.navigate('BeautyCart');
            }}>
            <Image source={ImagePath.cartimg} style={{height: 20, width: 20}} />
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          activeOpacity={1}
          style={{
            ...styles.searchBar,
            borderWidth: 1,
            borderColor: 'grey',
            marginHorizontal: 0,
            backgroundColor: 'white',
          }}>
          <Image
            source={ImagePath.searchimg}
            style={{height: 19, width: 19, marginHorizontal: 10}}
          />
          <View style={{flexDirection: 'row', marginVertical: 10}}>
            <Text style={styles.searchPrefix}>Search for </Text>
            <Animated.Text
              style={[
                styles.searchPrefix,
                {
                  opacity: fadeAnim,
                  transform: [
                    {translateY: translateYAnim},
                    {scale: animatedTextScale},
                  ],
                  color: animatedTextColor,
                },
              ]}>
              “{keywords[index]}”
            </Animated.Text>
          </View>
        </TouchableOpacity>
      </Animated.View>

      <Animated.ScrollView
        nestedScrollEnabled
        style={{flex: 1}}
        onScroll={Animated.event(
          [{nativeEvent: {contentOffset: {y: scrollY}}}],
          {useNativeDriver: true},
        )}
        scrollEventThrottle={16}>
        <Animated.View
          style={{
            transform: [{translateY: headerTranslateY}],
            opacity: headerOpacity,
            zIndex: 1,
          }}>
          <ImageBackground
            resizeMode="cover"
            style={{
              width: '100%',
              paddingTop:
                Platform.OS == 'android' ? StatusBar.currentHeight : 40,
              paddingBottom: 40,
            }}
            source={{
              uri: 'https://madamg.digittrix.com/images/unleash-banner.png',
            }}>
            <View style={styles.addressView}>
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {}}
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <Image
                  style={{height: 20.41, width: 16}}
                  source={ImagePath.locationimg}
                />
                <View style={{flex: 0.8}}>
                  {Global.gettingLocation ? (
                    <ActivityIndicator
                      style={{
                        alignSelf: 'flex-start',
                        marginVertical: 10,
                        marginLeft: 10,
                      }}
                      size={15}
                      color={'black'}
                    />
                  ) : (
                    <View>
                      <View style={styles.addressTextView}>
                        <Text style={styles.addressMainText}>
                          {Global.shortAddress}
                        </Text>
                        <Image
                          style={{marginLeft: 5}}
                          source={ImagePath.ArrowDown}
                        />
                      </View>
                      <Text numberOfLines={1} style={styles.addressText}>
                        {Global.fullAddress}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  props.navigation.navigate('BeautyCart');
                }}>
                <Image
                  source={ImagePath.cartimg}
                  style={{height: 20, width: 20}}
                />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              activeOpacity={0.1}
              style={{
                ...styles.searchBar,
              }}>
              <Image
                source={ImagePath.searchimg}
                style={{height: 19, width: 19, marginHorizontal: 10}}
              />
              <View style={{flexDirection: 'row', marginVertical: 10}}>
                <Text style={styles.searchPrefix}>Search for </Text>
                <Animated.Text
                  style={[
                    styles.searchPrefix,
                    {
                      opacity: fadeAnim,
                      transform: [
                        {translateY: translateYAnim},
                        {scale: animatedTextScale},
                      ],
                      color: animatedTextColor,
                    },
                  ]}>
                  “{keywords[index]}”
                </Animated.Text>
              </View>
            </TouchableOpacity>

            <View style={styles.bannerText}>
              <Text style={styles.bannerTitle}>Unleash Your Style</Text>
              <Text style={styles.bannerSubtitle}>
                Step into the world of fashion where trends meet timeless
                elegance.
              </Text>
              <Text style={styles.shopNowButton}>Shop Now</Text>
            </View>
          </ImageBackground>
        </Animated.View>

        <Text style={styles.categoryHeading}>{'SHOP BY CATEGORY'}</Text>
        <FlatList
          data={subCat}
          horizontal
          contentContainerStyle={{gap: 10, paddingHorizontal: 15}}
          renderItem={({item}) => (
            <View style={{paddingHorizontal: 0}}>
              <View
                style={{
                  borderWidth: 2,
                  borderColor: '#D52F6F',
                  borderRadius: 10,
                  overflow: 'hidden',
                }}>
                <DynamicImage
                  uri={`${IMAGE_BASE_URL}${item.image}`}
                  imgHeight={150}
                  imgWidth={170}
                />
                <LinearGradient
                  start={{x: 0.0, y: 0.0}}
                  end={{x: 0.0, y: 0.8}}
                  locations={[0.0, 1.0]}
                  colors={['#AD3499', '#6504B5']}
                  useViewFrame={true}>
                  <View style={{paddingVertical: 20, alignItems: 'center'}}>
                    <Text style={styles.categoryName}>{item.name}</Text>
                    <Text style={styles.categoryOffer}>{'20-50% OFF'}</Text>
                    <TouchableOpacity
                      onPress={() => {
                        props.navigation.navigate('AllProducts', {
                          slug: item.slug,
                          subCatId: item._id,
                        });
                      }}
                      style={styles.categoryBtn}>
                      <Image
                        source={{
                          uri: 'https://cdn-icons-png.flaticon.com/128/3114/3114931.png',
                        }}
                        style={{height: 20, width: 20}}
                      />
                    </TouchableOpacity>
                  </View>
                </LinearGradient>
              </View>
            </View>
          )}
        />

        {[...Array(50)].map((_, i) => (
          <Text key={i} style={{margin: 10}}>
            Text {i + 1}
          </Text>
        ))}
      </Animated.ScrollView>
    </SafeAreaView>
  );
};

export default ShopBeauty;

const styles = StyleSheet.create({
  addressView: {
    flexDirection: 'row',
    alignItems: 'center',
    fontSize: 14,
    fontFamily: designeSheet.QuicksandBold,
    color: 'black',
    marginHorizontal: 20,
  },
  locationIcon: {
    marginRight: 5,
    height: 20.41,
    width: 16,
  },
  addressTextView: {
    flexDirection: 'row',
    marginTop: 10,
    alignItems: 'center',
  },
  addressMainText: {
    marginLeft: 5,
    color: '#000000',
    fontFamily: designeSheet.QuicksandBold,
    fontSize: 14,
  },
  addressText: {
    marginLeft: 5,
    color: '#000000',
    fontSize: 13,
    fontFamily: designeSheet.QuicksandMedium,
  },
  searchBar: {
    backgroundColor: '#FFF7FB',
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 3,
  },
  searchPrefix: {
    fontSize: 14,
    color: '#6C6C6C',
    fontFamily: designeSheet.QuicksandBold,
  },
  bannerText: {
    flex: 1,
    width: '50%',
    height: 200,
    justifyContent: 'center',
    paddingLeft: 20,
  },
  bannerTitle: {
    fontSize: 18,
    fontFamily: designeSheet.QuicksandBold,
    color: '#B23CA6',
  },
  bannerSubtitle: {
    fontSize: 14,
    fontFamily: designeSheet.QuicksandRegular,
    color: 'black',
  },
  shopNowButton: {
    marginTop: 15,
    borderRadius: 5.12,
    fontFamily: designeSheet.QuicksandBold,
    backgroundColor: '#731A42',
    alignSelf: 'flex-start',
    color: 'white',
    padding: 8,
  },
  categoryHeading: {
    fontSize: 18,
    fontFamily: designeSheet.QuicksandSemiBold,
    color: '#000000',
    marginTop: -15,
    marginHorizontal: 15,
    marginBottom: 10,
  },
  categoryName: {
    fontSize: 16,
    fontFamily: designeSheet.QuicksandBold,
    color: 'white',
  },
  categoryOffer: {
    fontSize: 13,
    fontFamily: designeSheet.QuicksandMedium,
    color: 'white',
  },
  categoryBtn: {
    height: 40,
    width: 40,
    borderRadius: 25,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
});
