import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StatusBar,
  Image,
  FlatList,
  Dimensions,
} from 'react-native';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../../Designe/designeSheet';
import DrawerWrapper from '../SideDrawer/DrawerWrapper';
import {addToCart, getAllProducts, getFilters} from "../../../../Api's/Api";
import {API_URL, IMAGE_BASE_URL} from '../../../../utils/urls';
import Loader from '../../../../components/Loader';
import EmptyListContent from '../../../../components/EmptyListContent';
import Global from '../../../../Globals/Global';
import CustomToast from '../../../../components/CustomToast';

const AllProducts = ({navigation, route}) => {
  const data = route.params;

  const [isLoading, setIsLoading] = useState([]);
  const [allProducts, setAllProducts] = useState([]);
  const [filterData, setFilterData] = useState();
  const [toast, setToast] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    getFillters();
    getProducts();
  }, []);

  async function getFillters() {
    setIsLoading(true);
    try {
      const result = await getFilters(data.slug);
      if (result.status == 200) {
        setFilterData(result.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  async function getProducts() {
    setIsLoading(true);
    try {
      var url = `${API_URL.allProducts}?subCategory=${data.subCatId}&gender=&minPrice=200&maxPrice=20000&size=&color=&maxPercenatge=&minPercenatge=&page=1&limit=10&sort=`;
      console.log(url);

      const result = await getAllProducts(url);
      if (result.status == 200) {
        setAllProducts(result.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  async function addInCart(item) {
    console.log('sdjbcjsdb', item.variations[0]._id);
    console.log('userid', Global.guest_id);
    setIsLoading(true);
    var obj = {};

    obj =
      Global.isGuestUser == 'true'
        ? {
            itemType: 'shop_product',
            itemRef: item._id,
            quantity: 1,
            category: 'e-commerce',
            guestId: Global.guest_id.toString(),
            variationId: item.variations[0]._id,
          }
        : {
            itemType: 'shop_product',
            itemRef: item._id,
            quantity: 1,
            category: 'e-commerce',
            userId: Global.user_id.toString(),
            variationId: item.variations[0]._id,
          };
    console.log('sdjkbckjsbckjsb', obj);
    try {
      const result = await addToCart(obj);
      if (result.status == 200) {
        console.log('asjcbskjbc', result.data);
        setMessage('Item added to cart.');
        setToast(true);
        setTimeout(() => {
          navigation.navigate('EcommercCart');
        }, 1000);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    }
  }

  return (
    <DrawerWrapper
      navigation={navigation}
      setIsLoading={setIsLoading}
      setAllProducts={setAllProducts}
      filterData={filterData}
      data={data}>
      {toggleDrawer => (
        <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
          <StatusBar
            translucent={false}
            backgroundColor={'#FFF7FB'}
            barStyle={'dark-content'}
          />
          <View
            style={{
              backgroundColor: '#FFF7FB',
              justifyContent: 'center',
              padding: 20,
              borderBottomWidth: 1,
              borderColor: '#E0E0E0',
            }}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Image
                  source={ImagePath.headerarrow}
                  style={{height: 13, width: 20}}
                />
              </TouchableOpacity>
              <Text
                style={{
                  fontSize: 16,
                  color: '#87005F',
                  fontFamily: designeSheet.QuicksandBold,
                  textAlign: 'center',
                  flex: 1,
                }}>
                {'All Products'}
              </Text>
              <TouchableOpacity onPress={toggleDrawer}>
                <Image
                  source={{
                    uri: 'https://cdn-icons-png.flaticon.com/128/7855/7855877.png',
                  }}
                  style={{height: 20, width: 20}}
                />
              </TouchableOpacity>
            </View>
          </View>
          <View>
            <FlatList
              data={allProducts}
              numColumns={2}
              contentContainerStyle={{gap: 10, paddingVertical: 20}}
              ListEmptyComponent={() => (
                <EmptyListContent
                  text={'No Product Found!'}
                  image={
                    'https://cdn-icons-png.flaticon.com/128/17569/17569003.png'
                  }
                />
              )}
              renderItem={({item, index}) => (
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={() => {
                    navigation.navigate('ShopSingleProduct', {slug: item.slug});
                  }}
                  style={{
                    paddingHorizontal: 10,
                    width: Dimensions.get('screen').width / 2,
                  }}>
                  <View
                    style={{
                      borderRadius: 10,
                      overflow: 'hidden',
                      backgroundColor: 'white',
                      elevation: 5,
                      flex: 1,
                    }}>
                    <Image
                      source={{
                        uri: `${IMAGE_BASE_URL}${item.thumbnail}`,
                      }}
                      style={{height: 170}}
                    />
                    <View
                      style={{
                        paddingVertical: 0,
                        paddingHorizontal: 10,
                      }}>
                      <Text
                        style={{
                          fontSize: 15,
                          fontFamily: designeSheet.QuicksandBold,
                          color: '#000000',
                          marginTop: 5,
                        }}>
                        {item.name}
                      </Text>
                      <Text
                        numberOfLines={4}
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandBold,
                          color: 'grey',
                        }}>
                        {item.description}
                      </Text>
                      <Text>
                        <Text
                          style={{
                            color: '#000000',
                            fontSize: 16,
                            fontFamily: designeSheet.QuicksandSemiBold,
                          }}>
                          {'₹270'}
                        </Text>{' '}
                        <Text
                          style={{
                            color: '#6C6C6C',
                            fontSize: 12,
                            fontFamily: designeSheet.QuicksandRegular,
                            textDecorationLine: 'line-through',
                          }}>
                          {'₹300'}
                        </Text>
                      </Text>
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: designeSheet.QuicksandSemiBold,
                          color: '#05945B',
                        }}>
                        {`10% OFF`}
                      </Text>

                      <Text
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandMedium,
                          color: 'white',
                        }}>
                        {'20-50% OFF'}
                      </Text>
                    </View>
                    <View style={{flex: 1}} />
                    <TouchableOpacity
                      onPress={() => {
                        addInCart(item);
                      }}
                      activeOpacity={1}
                      style={{
                        paddingVertical: 5,
                        backgroundColor: 'black',
                        borderRadius: 5,
                        marginBottom: 10,
                        marginHorizontal: 10,
                      }}>
                      <Text
                        style={{
                          fontSize: 13,
                          fontFamily: designeSheet.QuicksandBold,
                          color: 'white',
                          alignSelf: 'center',
                        }}>
                        Add to Cart
                      </Text>
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              )}
            />
          </View>

          <Loader isActive={isLoading} />
          {toast && <CustomToast setToast={setToast} message={message} />}
        </View>
      )}
    </DrawerWrapper>
  );
};

export default AllProducts;
