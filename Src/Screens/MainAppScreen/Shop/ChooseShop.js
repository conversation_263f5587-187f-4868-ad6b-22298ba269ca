import {
  Dimensions,
  Image,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import HeaderComp from '../../../components/HeaderComp';
import {shopCategories} from "../../../Api's/Api";
import Loader from '../../../components/Loader';
import {IMAGE_BASE_URL} from '../../../utils/urls';
import designeSheet from '../../../Designe/designeSheet';

const ChooseShop = props => {
  const [shopCat, setShopCat] = useState([]);
  const [isLoading, setIsLoading] = useState([]);

  useEffect(() => {
    getCat();
  }, []);

  async function getCat() {
    setIsLoading(true);
    try {
      const result = await shopCategories();
      if (result.status == 200) {
        setShopCat(result.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#FFF7FB',
      }}>
      <HeaderComp
        title={'Fashion & Beauty Awaits'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View style={{gap: 20, alignSelf: 'center', marginTop: 20}}>
        {shopCat.map((item, index) => (
          <View style={{overflow: 'hidden'}}>
            <TouchableOpacity
              activeOpacity={1}
              style={{overflow: 'hidden'}}
              onPress={() => {
                props.navigation.navigate('ShopBeauty', {slug: item.slug});
              }}>
              <Image
                source={{uri: `${IMAGE_BASE_URL}${item.image}`}}
                style={{
                  height: 197,
                  width: Dimensions.get('screen').width - 40,
                  borderRadius: 20,
                  overflow: 'hidden',
                }}
              />
            </TouchableOpacity>
            <View
              style={{
                position: 'absolute',
                right: 0,
                bottom: 0,
                padding: 10,
                backgroundColor: '#FFF7FB',
                borderBottomRightRadius: 20,
                borderTopLeftRadius: 20,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  color: 'black',
                  fontFamily: designeSheet.QuicksandBold,
                  textAlign: 'center',
                  marginBottom: 5,
                }}>
                {item.name}
              </Text>
            </View>
          </View>
        ))}
      </View>
      <Loader isActive={isLoading} />
    </View>
  );
};

export default ChooseShop;

const styles = StyleSheet.create({});
