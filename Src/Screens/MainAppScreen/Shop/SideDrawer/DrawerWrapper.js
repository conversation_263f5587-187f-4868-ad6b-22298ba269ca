import React, {useState, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Dimensions,
  Image,
} from 'react-native';
import designeSheet from '../../../../Designe/designeSheet';
import {getAllProducts} from "../../../../Api's/Api";
import {API_URL} from '../../../../utils/urls';
import MultiSlider from '@ptomasroos/react-native-multi-slider';

const {width} = Dimensions.get('window');

var gender = '';
var color = '';
var sortBy = '';
var size = '';

const DrawerWrapper = ({
  children,
  navigation,
  setIsLoading,
  setAllProducts,
  filterData,
  data,
}) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const slideAnim = useRef(new Animated.Value(-width * 0.55)).current;
  const [minPrice, setMinPrice] = useState(200);
  const [maxPrice, setMaxPrice] = useState(20000);
  const sortOptions = [
    {value: 'productAsc', label: 'Old to New'},
    {value: 'productDesc', label: 'New to Old'},
    {value: 'priceAsc', label: 'Price: Low to High'},
    {value: 'priceDesc', label: 'Price: High to Low'},
  ];

  // const [gender, setGender] = useState('female');

  const toggleDrawer = () => {
    if (isDrawerOpen) {
      Animated.timing(slideAnim, {
        toValue: -width * 0.55,
        duration: 250,
        useNativeDriver: true,
      }).start(() => setIsDrawerOpen(false));
    } else {
      setIsDrawerOpen(true);
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  };

  async function getProducts() {
    setIsLoading(true);
    try {
      var url = `${API_URL.allProducts}?subCategory=${
        data.subCatId
      }&gender=${gender.toLowerCase()}&minPrice=${minPrice}&maxPrice=${maxPrice}&size${size}=&color=${color}&maxPercenatge=&minPercenatge=&page=1&limit=10&sort=${sortBy}`;
      console.log('URLs', url);
      const result = await getAllProducts(url);
      if (result.status == 200) {
        setAllProducts(result.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <View style={{flex: 1}}>
      {/* Main Content */}
      {children(toggleDrawer)}

      {/* Overlay */}
      {isDrawerOpen && (
        <TouchableOpacity
          onPress={toggleDrawer}
          style={styles.overlay}
          activeOpacity={1}
        />
      )}

      {/* Drawer */}
      <Animated.View
        style={[styles.drawer, {transform: [{translateX: slideAnim}]}]}>
        <TouchableOpacity onPress={toggleDrawer}>
          <Image
            source={{
              uri: 'https://cdn-icons-png.flaticon.com/128/2976/2976286.png',
            }}
            style={{
              height: 15,
              width: 15,
              marginBottom: 40,
              alignSelf: 'flex-end',
            }}
          />
        </TouchableOpacity>
        <Text style={styles.drawerTitle}>Filters</Text>
        <Text style={styles.header}>SorBy</Text>
        {sortOptions.map((item, index) => (
          <TouchableOpacity
            onPress={() => {
              // setGender(item);
              sortBy = item.value;
              getProducts();
            }}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 5,
              gap: 5,
            }}>
            {sortBy == item.value ? (
              <View
                style={{
                  height: 10,
                  width: 10,
                  borderWidth: 1,
                  borderRadius: 5,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    height: 5,
                    width: 5,
                    borderRadius: 5,
                    backgroundColor: 'black',
                  }}
                />
              </View>
            ) : (
              <View
                style={{height: 10, width: 10, borderWidth: 1, borderRadius: 5}}
              />
            )}
            <Text style={styles.text}>{item.label}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.header}>Gender</Text>
        {['Men', 'Women', 'Kids'].map((item, index) => (
          <TouchableOpacity
            onPress={() => {
              // setGender(item);
              gender = item;
              getProducts();
            }}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 5,
              gap: 5,
            }}>
            {gender == item ? (
              <View
                style={{
                  height: 10,
                  width: 10,
                  borderWidth: 1,
                  borderRadius: 5,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    height: 5,
                    width: 5,
                    borderRadius: 5,
                    backgroundColor: 'black',
                  }}
                />
              </View>
            ) : (
              <View
                style={{height: 10, width: 10, borderWidth: 1, borderRadius: 5}}
              />
            )}
            <Text style={styles.text}>{item}</Text>
          </TouchableOpacity>
        ))}
        {filterData == undefined || filterData.colors.length == 0 ? null : (
          <>
            <Text style={styles.header}>Colors</Text>
            {filterData.colors.map((item, index) => (
              <TouchableOpacity
                onPress={() => {
                  color = item._id;
                  getProducts();
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 5,
                  gap: 5,
                }}>
                {color == item._id ? (
                  <View
                    style={{
                      height: 10,
                      width: 10,
                      borderWidth: 1,
                      borderRadius: 5,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        height: 5,
                        width: 5,
                        borderRadius: 5,
                        backgroundColor: 'black',
                      }}
                    />
                  </View>
                ) : (
                  <View
                    style={{
                      height: 10,
                      width: 10,
                      borderWidth: 1,
                      borderRadius: 5,
                    }}
                  />
                )}
                <Text style={styles.text}>{item.name}</Text>
              </TouchableOpacity>
            ))}
          </>
        )}

        {filterData == undefined || filterData.sizes.length == 0 ? null : (
          <>
            <Text style={styles.header}>Colors</Text>
            {filterData.sizes.map((item, index) => (
              <TouchableOpacity
                onPress={() => {
                  size = item._id;
                  getProducts();
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 5,
                  gap: 5,
                }}>
                {size == item._id ? (
                  <View
                    style={{
                      height: 10,
                      width: 10,
                      borderWidth: 1,
                      borderRadius: 5,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <View
                      style={{
                        height: 5,
                        width: 5,
                        borderRadius: 5,
                        backgroundColor: 'black',
                      }}
                    />
                  </View>
                ) : (
                  <View
                    style={{
                      height: 10,
                      width: 10,
                      borderWidth: 1,
                      borderRadius: 5,
                    }}
                  />
                )}
                <Text style={styles.text}>{item.name}</Text>
              </TouchableOpacity>
            ))}
          </>
        )}

        <Text style={styles.header}>Price Range</Text>

        <View style={{marginBottom: 20}}>
          <Text style={styles.text}>
            ₹{minPrice} - ₹{maxPrice}
          </Text>

          <MultiSlider
            values={[minPrice, maxPrice]}
            min={200}
            max={20000}
            step={100}
            sliderLength={width * 0.45} // Adjust based on your drawer width
            onValuesChange={values => {
              setMinPrice(values[0]);
              setMaxPrice(values[1]);
            }}
            onValuesChangeFinish={getProducts}
            selectedStyle={{backgroundColor: '#87005F'}}
            unselectedStyle={{backgroundColor: '#ccc'}}
            markerStyle={{
              backgroundColor: '#87005F',
              height: 15,
              width: 15,
            }}
          />
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  drawer: {
    position: 'absolute',
    width: width * 0.55,
    height: '100%',
    backgroundColor: '#FFF7FB',
    paddingTop: 10,
    paddingHorizontal: 20,
    zIndex: 10,
  },
  drawerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#87005F',
    marginBottom: 20,
  },
  header: {
    fontSize: 16,
    color: 'black',
    marginVertical: 10,
    fontFamily: designeSheet.QuicksandSemiBold,
  },
  text: {
    fontSize: 14,
    color: 'black',
    fontFamily: designeSheet.QuicksandMedium,
    lineHeight: 20,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#00000066',
    zIndex: 5,
  },
});

export default DrawerWrapper;
