import {StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import ToggleButton from '../../components/ToggleButton';
import designeSheet from '../../Designe/designeSheet';

const Notifications = props => {
  const [notiType, setNotiType] = useState([
    {
      text1: 'App Notifications',
      text2: 'Receive mobile app notifications',
    },
    {
      text1: 'Booking Reminders',
      text2: 'Receive trigger notifications',
    },
    {
      text1: 'Payment & Billing Alerts',
      text2: 'Stay update about invoices & payments',
    },
    {
      text1: 'All Daily Alerts',
      text2: 'Receive scheduled notifications',
    },
  ]);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Notification Preference'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />

      <View style={{marginTop: 35}}>
        {notiType.map((item, index) => (
          <View key={index}>
            <View style={{
              flexDirection: 'row', 
              marginHorizontal: 20
              }}>
              <View style={{flex: 1}}>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#111619',
                  }}>
                  {item.text1}
                </Text>
                <Text
                  style={{
                    fontSize: 15,
                    fontFamily: designeSheet.QuicksandRegular,
                    color: '#838383',
                  }}>
                  {item.text2}
                </Text>
              </View>
              <ToggleButton />
            </View>
            <View
              style={{
                borderWidth: 1,
                borderColor: '#3C444817',
                marginTop: 20,
                marginHorizontal: 20,
                marginBottom: 20,
              }}
            />
          </View>
        ))}
      </View>
    </View>
  );
};

export default Notifications;

const styles = StyleSheet.create({});
