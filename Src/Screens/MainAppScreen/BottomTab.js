import {
  Animated,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useMemo, useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import Home from './Home/Home';
import designeSheet from '../../Designe/designeSheet';
import {setbottomvalue, setData} from '../../Redux/CreatSlice';
import MyBooking from './MyBooking';
import Refer from './Refer';
import Profile from './Profile';
import Global from '../../Globals/Global';
import SelectAddress from './Carts/Popups/SelectAddress';

export default function BottomTab(props) {
  const dispatch = useDispatch();
  const bottomvalue = useSelector(state => state.counter.bottomtabvalue);
  const headerHeight1 = useRef(new Animated.Value(0)).current;
  const [selectAddressPopup, setSelectAddressPopup] = useState(false);

  const isGuest = Global.isGuestUser === 'true';

  const data = useMemo(() => {
    const tabs = [
      {
        image1: ImagePath.homefill,
        image: ImagePath.homefill,
        title: 'Home',
      },
    ];

    if (!isGuest) {
      tabs.push({
        image1: ImagePath.service,
        image: ImagePath.pinkbooking,
        title: 'Bookings',
      });

      tabs.push({
        image1: ImagePath.message,
        image: ImagePath.pinkrefer,
        title: 'Refer',
      });
    }

    tabs.push({
      image1: ImagePath.noti,
      image: ImagePath.pinkaccount,
      title: isGuest ? 'Login' : 'Account',
    });

    return tabs;
  }, [isGuest]);

  const renderScreen = () => {
    if (bottomvalue === 0)
      return (
        <Home
          props={props}
          setSelectAddressPopup={setSelectAddressPopup}
          headerHeight1={headerHeight1}
        />
      );
    if (bottomvalue === 1 && !isGuest)
      return <MyBooking navigation={props.navigation} />;
    if (bottomvalue === 2 && !isGuest)
      return <Refer navigation={props.navigation} />;

    // Adjust index for guest user
    const accountIndex = isGuest ? 1 : 3;
    if (bottomvalue === accountIndex)
      return <Profile navigation={props.navigation} />;

    return null;
  };

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <View style={{flex: 1}}>{renderScreen()}</View>

      <View style={{backgroundColor: 'white'}}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: 'white',
            marginTop: 5,
          }}>
          {data.map((item, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                if (item.title === 'Login') {
                  Global.initialRoute = 'Login';
                  dispatch(setData('0'));
                } else {
                  dispatch(setbottomvalue(index));
                }
              }}
              style={{
                flex: 1,
                rowGap: 5,
                paddingVertical: Platform.OS === 'android' ? 8 : 20,
                alignItems: 'center',
              }}>
              <Image
                source={bottomvalue === index ? item.image : item.image1}
                style={{
                  height: index === 0 ? 20 : 24,
                  width: index === 0 ? 40 : 24,
                }}
              />
              <Text
                style={{
                  color: bottomvalue === index ? '#E92E89' : '#6C6C6C',
                  fontSize: 11,
                  alignSelf: 'center',
                  fontFamily: designeSheet.QuicksandMedium,
                }}>
                {item.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      {selectAddressPopup ? (
        <SelectAddress
          headerHeight={headerHeight1}
          setSelectAddressPopup={setSelectAddressPopup}
          props={props}
        />
      ) : null}
    </View>
  );
}

const styles = StyleSheet.create({});
