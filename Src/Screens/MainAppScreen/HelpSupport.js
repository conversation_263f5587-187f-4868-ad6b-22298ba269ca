import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';

const HelpSupport = props => {
  const [qus, setQus] = useState([
    {
      qusdata: 'How do I book an appointment?',
    },
    {
      qusdata: 'Can I reschedule or cancel my appointment?',
    },
    {
      qusdata: 'Can I book multiple services at once?',
    },
    {
      qusdata: 'How do I delete my account?',
    },
  ]);
  const [activeIndex, setActiveIndex] = useState(null);

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Help & Support'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginHorizontal: 20,
          marginTop: 22,
        }}>
        <Image
          source={ImagePath.help}
          style={{
            height: 94,
            width: 128,
          }}
        />
        <View
          style={{
            marginHorizontal: 20,
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
            }}>
            {'We are here to help you'}
          </Text>
          <TouchableOpacity
          onPress={()=> {
            props.navigation.navigate('ChatSupport');
          }}
            style={{
              backgroundColor: '#000000',
              borderRadius: 4,
              alignItems: 'center',
              justifyContent: 'center',
              height: 32,
              width: 126,
              marginTop: 5,
            }}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#FCFCFC',
              }}>
              {'Chat with us'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      <Text
        style={{
          fontSize: 16,
          color: '#000000',
          fontFamily: designeSheet.QuicksandSemiBold,
          marginHorizontal: 20,
          marginTop: 23,
        }}>
        {'Most Asked Questions'}
      </Text>
      <View style={{marginTop: 15}}>
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCCCC',
            marginHorizontal: 20,
          }}
        />

        {qus.map((item, index) => (
          <View
            key={index}
            style={{
              marginHorizontal: 20,
            }}>
            <TouchableOpacity
              onPress={() => {
                setActiveIndex(activeIndex === index ? null : index);
              }}
              style={{
                flexDirection: 'row',
                marginTop: 15,
                marginBottom: activeIndex === index ? 5 : 15,
              }}>
              <Text
                style={{
                  flex: 1,
                  fontSize: 15,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#6C6C6C',
                }}>
                {item.qusdata}
              </Text>

              <Image
                source={
                  activeIndex === index ? ImagePath.homeimg1 : ImagePath.dropup
                }
                style={{
                  height: 24,
                  width: 12,
                  tintColor: '#6C6C6C',
                }}
              />
            </TouchableOpacity>
            {activeIndex === index && (
              <Text
                style={{
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandRegular,
                  color: '#6C6C6C',
                  marginBottom: 10,
                }}>
                {
                  'Yes, you can easily reschedule or cancel your appointment through the app. Simply go to the “My Bookings” or “Upcoming Appointments” section, select the appointment you’d like to change, and follow the prompts to reschedule or cancel. Some salons may have specific rescheduling or cancellation policies, such as requiring a minimum notice period or charging a fee, so it’s a good idea to check the salon’s policy in the appointment details or reach out to them directly. If you need help, our support team is always here to assist you!'
                }
              </Text>
            )}

            <View
              style={{
                borderWidth: 1,
                borderColor: '#CCCCCCCC',
              }}
            />
          </View>
        ))}
      </View>
    </View>
  );
};

export default HelpSupport;

const styles = StyleSheet.create({});
