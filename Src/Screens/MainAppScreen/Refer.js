import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const Refer = props => {
  const [earn, setEarn] = useState([{}, {}, {}]);
  const [selectedTab, setSelectedTab] = useState('invite');

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Refer & Earn'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View
        style={{
          flexDirection: 'row',
          marginTop: 20,
          justifyContent: 'space-between',
          alignItems: 'center',
          marginHorizontal: 20,
        }}>
        <TouchableOpacity onPress={() => setSelectedTab('invite')}>
          <Text
            style={{
              fontSize: 15,
              color: '#000000',
              fontFamily: designeSheet.QuicksandSemiBold,
              marginHorizontal: 30,
              marginVertical: 10,
              textDecorationLine: selectedTab === 'invite' ? 'underline' : '',
              textDecorationStyle: selectedTab === 'invite' ? 'solid' : '',
            }}>
            {'Invite & Earn'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setSelectedTab('reward')}>
          <Text
            style={{
              fontSize: 15,
              color: '#000000',
              fontFamily: designeSheet.QuicksandSemiBold,
              marginHorizontal: 30,
              marginVertical: 10,
              textDecorationLine: selectedTab === 'reward' ? 'underline' : '',
              textDecorationStyle: selectedTab === 'reward' ? 'solid' : '',
            }}>
            {'Rewards'}
          </Text>
        </TouchableOpacity>
      </View>
      {selectedTab == 'invite' ? (
        <View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginTop: 10,
            }}>
            <View>
              <Text
                style={{
                  fontSize: 14,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginHorizontal: 30,
                  marginTop: 20,
                }}>
                {'Refer & Earn'}
              </Text>
              <Text
                style={{
                  fontSize: 18,
                  color: '#E92E89',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginHorizontal: 30,
                }}>
                {'Upto ₹44,99'}
              </Text>
              <View
                style={{
                  borderWidth: 1,
                  borderColor: '#E92E89',
                  marginHorizontal: 20,
                  marginTop: 20,
                  borderStyle: 'dotted',
                }}>
                <View
                  style={{
                    marginVertical: 10,
                    marginHorizontal: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <View>
                    <Text
                      style={{
                        fontSize: 12,
                        color: '#000000',
                        fontFamily: designeSheet.QuicksandSemiBold,
                      }}>
                      {'Your referral code'}
                    </Text>
                    <Text
                      style={{
                        fontSize: 12,
                        color: '#000000',
                        fontFamily: designeSheet.QuicksandSemiBold,
                      }}>
                      {'7B87SR'}
                    </Text>
                  </View>
                  <View
                    style={{
                      borderWidth: 1,
                      height: 20,
                      width: 0,
                      marginHorizontal: 10,
                      marginTop: 3,
                      marginBottom: 3,
                      borderColor: '#E92E89',
                      borderStyle: 'dotted',
                    }}
                  />
                  <Image
                    source={ImagePath.copy}
                    style={{height: 24, width: 24}}
                  />
                </View>
              </View>
            </View>
            <Image
              source={ImagePath.refer}
              style={{height: 121, width: 132, marginHorizontal: 20}}
            />
          </View>
          <TouchableOpacity
            style={{
              backgroundColor: '#000000',
              marginHorizontal: 20,
              marginTop: 25,
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 4,
            }}>
            <Text
              style={{
                fontFamily: designeSheet.QuicksandSemiBold,
                fontSize: 16,
                color: '#FFFFFF',
                marginVertical: 10,
              }}>
              {'Refer Now'}
            </Text>
          </TouchableOpacity>
          <View style={{marginTop: 20}}>
            {earn.map((item, index) => (
              <View
                key={index}
                style={{
                  flexDirection: 'row',
                  marginHorizontal: 20,
                  alignItems: 'center',
                  gap: 10,
                  marginVertical: 5,
                }}>
                <Text
                  style={{
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandBold,
                    fontSize: 20,
                  }}>
                  {'.'}
                </Text>
                <Text
                  style={{
                    flex: 1,
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandMedium,
                    fontSize: 15,
                  }}>
                  {'Earn ₹150 on the first successful booking of the refree.'}
                </Text>
              </View>
            ))}
          </View>
        </View>
      ) : (
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 12,
            marginHorizontal: 20,
            marginTop: 20,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 10,
              marginVertical: 20,
            }}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  color: '#000000',
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandSemiBold,
                }}>
                {'Total Madam G Credits Earned'}
              </Text>
              <Text
                style={{
                  color: '#000000',
                  fontSize: 18,
                  fontFamily: designeSheet.QuicksandSemiBold,
                }}>
                {'₹2,000'}
              </Text>
            </View>
            <Image
              source={ImagePath.rewardImg}
              style={{height: 76, width: 100}}
            />
          </View>
        </View>
      )}
    </View>
  );
};

export default Refer;

const styles = StyleSheet.create({});
