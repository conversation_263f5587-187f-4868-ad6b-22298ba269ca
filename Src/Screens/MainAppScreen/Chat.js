// import {Image, Platform, StyleSheet, Text, TextInput, View} from 'react-native';
// import React, {useState} from 'react';
// import ImagePath from '../../Assets/ImagePath/ImagePath';
// import designeSheet from '../../Designe/designeSheet';

// const Chat = () => {
//   const [message, setMessage] = useState('');
//   return (
//     <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
//       <View
//         style={{
//           flexDirection: 'row',
//           alignItems: 'center',
//           marginHorizontal: 20,
//           marginTop: Platform.OS == 'android' ? 20 : 50,
//           marginBottom: 10,
//         }}>
//         <View style={{}}>
//           <Image
//             source={ImagePath.headerarrow}
//             style={{height: 13, width: 20}}
//           />
//         </View>
//         <View style={{flexDirection: 'row', flex: 1, alignItems: 'center'}}>
//           <Image
//             source={ImagePath.reviewimg}
//             style={{height: 40, width: 40, marginHorizontal: 20}}
//           />
//           <Text
//             style={{
//               fontSize: 18,
//               fontFamily: designeSheet.QuicksandSemiBold,
//               color: '#000000',
//             }}>
//             {'Riya Sharma'}
//           </Text>
//         </View>
//         <Image source={ImagePath.doted} style={{height: 16, width: 16}} />
//       </View>
//       <View style={{flex: 1, backgroundColor: '#EEEEEE'}}>
//         <View
//           style={{
//             backgroundColor: '#FFFFFF',
//             padding: 5,
//             marginTop: 20,
//             marginHorizontal: 10,
//             borderTopLeftRadius: 16,
//             borderTopRightRadius: 16,
//             borderBottomRightRadius: 16,
//             alignItems: 'flex-start',
//             justifyContent: 'center',
//           }}>
//           <Text
//             style={{
//               fontSize: 14,
//               color: '#4D4D4D',
//               fontFamily: designeSheet.QuicksandRegular,
//               marginHorizontal: 10,
//               textAlign: 'center',
//             }}>
//             {'Hey there! 👋'}
//           </Text>
//           <Text
//             style={{
//               fontSize: 14,
//               color: '#4D4D4D',
//               fontFamily: designeSheet.QuicksandRegular,
//               marginHorizontal: 10,
//               textAlign: 'center',
//             }}>
//             {'10:10'}
//           </Text>
//         </View>

//         <View
//           style={{
//             backgroundColor: '#FFFFFF',
//             padding: 5,
//             marginTop: 20,
//             marginHorizontal: 10,
//             borderTopLeftRadius: 16,
//             borderTopRightRadius: 16,
//             borderBottomRightRadius: 16,
//             alignItems: 'flex-start',
//             justifyContent: 'center',
//           }}>
//           <Text
//             style={{
//               fontSize: 14,
//               color: '#4D4D4D',
//               fontFamily: designeSheet.QuicksandRegular,
//               marginHorizontal: 10,
//               textAlign: 'center',
//             }}>
//             {'I’m available at 2 PM today! For your spa & salon bookings!'}
//           </Text>
//           <Text
//             style={{
//               fontSize: 14,
//               color: '#4D4D4D',
//               fontFamily: designeSheet.QuicksandRegular,
//               marginHorizontal: 10,
//               textAlign: 'center',
//             }}>
//             {'10:10'}
//           </Text>
//         </View>
//       </View>
//       <View style={{backgroundColor: '#FFF7FB'}}>
//         <View style={{flexDirection: 'row', alignItems: 'center'}}>
//           <View
//             style={{
//               flex: 1,
//               flexDirection: 'row',
//               alignItems: 'center',
//               borderWidth: 0.5,
//               borderColor: '#CCCCCCCC',
//               marginHorizontal: 5,
//               marginTop: 10,
//               marginBottom: Platform.OS == 'android' ? 10 : 20,
//               borderRadius: 8,
//             }}>
//             <TextInput
//               placeholder="Type a message ..."
//               style={{
//                 flex: 1,
//                 padding: 0,
//                 marginVertical: Platform.OS == 'ios' ? 20 : 0,
//                 borderRadius: 12,
//                 marginHorizontal: 20,
//                 marginTop: 10,
//                 fontSize: 14,
//                 fontFamily: designeSheet.QuicksandRegular,
//               }}
//               placeholderTextColor={'#5B5F5F'}
//               value={message}
//               onChangeText={text => setMessage(text)}
//             />
//             <Image source={ImagePath.camera} style={{height: 24, width: 24}} />
//             <Image
//               source={ImagePath.gallery}
//               style={{height: 24, width: 24, marginHorizontal: 10}}
//             />
//           </View>
//           <Image
//             source={ImagePath.sendmsg}
//             style={{height: 50, width: 50, marginHorizontal: 5}}
//           />
//         </View>
//       </View>
//     </View>
//   );
// };

// export default Chat;

// const styles = StyleSheet.create({});

import {
  Image,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  View,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import React, {useState} from 'react';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';

const Chat = () => {
  const [message, setMessage] = useState('');

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity>
          <Image source={ImagePath.headerarrow} style={styles.backIcon} />
        </TouchableOpacity>
        <View style={styles.userInfo}>
          <Image source={ImagePath.reviewimg} style={styles.userImage} />
          <Text style={styles.username}>Riya Sharma</Text>
        </View>
        <Image source={ImagePath.doted} style={styles.dotsIcon} />
      </View>

      {/* Messages */}
      <ScrollView style={styles.chatArea}>
        <LeftMessage text="Hey there! 👋" time="10:10" />
        <LeftMessage
          text="I’m available at 2 PM today! For your spa & salon bookings!"
          time="10:10"
        />
        <RightMessage
          text="Great! I will wait for you at sharp 2 PM today."
          time="11:22"
        />
      </ScrollView>

      {/* Input Area */}
      <View style={styles.inputContainer}>
        <View style={styles.inputBox}>
          <TextInput
            placeholder="Type a message ..."
            placeholderTextColor="#5B5F5F"
            value={message}
            onChangeText={setMessage}
            style={styles.textInput}
          />
          <Image source={ImagePath.camera} style={styles.icon} />
          <Image
            source={ImagePath.gallery}
            style={[styles.icon, {marginLeft: 10}]}
          />
        </View>
        <TouchableOpacity>
          <Image source={ImagePath.sendmsg} style={styles.sendIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const LeftMessage = ({text, time}) => (
  <View style={styles.leftMessage}>
    <Text style={styles.messageText}>{text}</Text>
    <Text style={styles.messageTime}>{time}</Text>
  </View>
);

const RightMessage = ({text, time}) => (
  <View style={styles.rightMessage}>
    <Text style={styles.messageTextRight}>{text}</Text>
    <Text style={styles.messageTimeRight}>{time}</Text>
  </View>
);

export default Chat;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF7FB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: Platform.OS === 'android' ? 20 : 50,
    marginBottom: 10,
  },
  backIcon: {
    height: 13,
    width: 20,
  },
  userInfo: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  userImage: {
    height: 40,
    width: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  username: {
    fontSize: 18,
    fontFamily: designeSheet.QuicksandSemiBold,
    color: '#000000',
  },
  dotsIcon: {
    height: 16,
    width: 16,
  },
  chatArea: {
    flex: 1,
    backgroundColor: '#EEEEEE',
    paddingHorizontal: 10,
  },
  leftMessage: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderBottomRightRadius: 16,
    alignSelf: 'flex-start',
    marginTop: 20,
    padding: 10,
    maxWidth: '80%',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  rightMessage: {
    backgroundColor: '#0066CC',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderBottomLeftRadius: 16,
    alignSelf: 'flex-end',
    marginTop: 20,
    padding: 10,
    maxWidth: '80%',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  messageText: {
    fontSize: 14,
    color: '#4D4D4D',
    fontFamily: designeSheet.QuicksandRegular,
  },
  messageTextRight: {
    fontSize: 14,
    color: '#FFFFFF',
    fontFamily: designeSheet.QuicksandRegular,
  },
  messageTime: {
    fontSize: 12,
    color: '#888',
    alignSelf: 'flex-end',
    marginTop: 5,
    fontFamily: designeSheet.QuicksandRegular,
  },
  messageTimeRight: {
    fontSize: 12,
    color: '#E0E0E0',
    alignSelf: 'flex-end',
    marginTop: 5,
    fontFamily: designeSheet.QuicksandRegular,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF7FB',
    padding: 10,
    marginBottom: Platform.OS == 'android' ? 2 : 20,
  },
  inputBox: {
    flex: 1,
    flexDirection: 'row',
    borderWidth: 0.5,
    borderColor: '#CCCCCC',
    borderRadius: 8,
    paddingHorizontal: 10,
    alignItems: 'center',
  },
  textInput: {
    flex: 1,
    fontSize: 14,
    fontFamily: designeSheet.QuicksandRegular,
    paddingVertical: Platform.OS === 'ios' ? 15 : 15,
    color: '#000',
  },
  icon: {
    height: 24,
    width: 24,
  },
  sendIcon: {
    height: 50,
    width: 50,
    marginLeft: 5,
  },
});
