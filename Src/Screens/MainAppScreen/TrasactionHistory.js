import {Image, ScrollView, StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const TrasactionHistory = props => {
  const [TrasactionData, setTrasactionData] = useState([
    {
      image: ImagePath.t1,
      amount: '+1200.00',
      color: '#0A985F',
    },
    {
      image: ImagePath.t2,
      amount: '-120.00',
      color: '#FF1100',
    },
    {
      image: ImagePath.t3,
      amount: '+1900.00',
      color: '#0A985F',
    },
    {
      image: ImagePath.t4,
      amount: '+9200.00',
      color: '#0A985F',
    },
    {
      image: ImagePath.t5,
      amount: '-1240.00',
      color: '#FF1100',
    },
    {
      image: ImagePath.t6,
      amount: '-874.00',
      color: '#FF1100',
    },
    {
      image: ImagePath.t7,
      amount: '+2450.00',
      color: '#0A985F',
    },
    {
      image: ImagePath.t8,
      amount: '+5870.00',
      color: '#0A985F',
    },
    {
      image: ImagePath.t9,
      amount: '-5870.00',
      color: '#FF1100',
    },
  ]);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Transactions'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <ScrollView>
        <View style={{marginTop: 20}}>
          <View
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCC',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              backgroundColor: '#F1F6FF',
            }}>
            <View style={{marginHorizontal: 13, marginVertical: 10}}>
              <Text
                style={{
                  fontSize: 18,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {'2025'}
              </Text>
              <Text
                style={{
                  fontSize: 18,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#000000',
                }}>
                {'June'}
              </Text>
            </View>
            <Text
              style={{
                fontSize: 20,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#0A985F',
                marginHorizontal: 13,
                marginVertical: 10,
              }}>
              {'+34,897.08'}
            </Text>
          </View>
        </View>
        {TrasactionData.map((item, index) => (
          <View
            key={index}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 13,
              marginTop: 20,
            }}>
            <Image source={item.image} style={{height: 47, width: 47}} />
            <View style={{flex: 1, marginHorizontal: 13}}>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#000000',
                }}>
                {'Anjali Sharma'}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandRegular,
                  color: '#6C6C6C',
                }}>
                {'15 June 2025 - 10:45am'}
              </Text>
            </View>

            <Text
              style={{
                fontSize: 16,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: item.color,
                marginHorizontal: 13,
              }}>
              {item.amount}
            </Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default TrasactionHistory;

const styles = StyleSheet.create({});
