import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';

const PrivacyPolicy = props => {
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Privacy Policy'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <Text
        style={{
          fontSize: 12,
          fontFamily: designeSheet.QuicksandRegular,
          color: '#6C6C6C',
          marginHorizontal: 20,marginTop: 23
        }}>
        {
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sem odio enim ut nullam tortor, bibendum interdum. Varius at amet, dignissim morbi ac pulvinar eu blandit lorem. Est pellentesque bibendum quam odio ac, tortor sit. Sed tellus at tellus amet mi.dignissim morbi ac pulvinar eu blandit lorem. Est pellentesque bibendum quam odio ac, tortor sit. Sed tellus at tellus amet mi.'
        }
      </Text>
    </View>
  );
};

export default PrivacyPolicy;

const styles = StyleSheet.create({});
