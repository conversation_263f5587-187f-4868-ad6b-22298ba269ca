import {
  Animated,
  Image,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import Loader from '../../components/Loader';
import {getOrders, startCall} from "../../Api's/Api";
import moment from 'moment';
import Global from '../../Globals/Global';
import {sessionModel} from '../../Modals/userDataModal';

const MyBooking = props => {
  const headerHeight = useRef(new Animated.Value(0)).current;
  const [status, setStatus] = useState([
    {
      text: 'Booking Confirmed ,  01 June 2025',
      img: ImagePath.confirm,
    },
    {
      text: 'Beautician Assigned – 03 June 2025',
      img: ImagePath.confirm,
    },
    {
      text: 'Your beautician is en route to your location',
      img: ImagePath.confirm1,
    },
    {
      text: 'Your service session has started.',
      img: ImagePath.confirm1,
    },
    {
      text: 'Your booking has been successfully completed.',
      img: ImagePath.confirm1,
    },
  ]);
  const [help, setHelp] = useState(false);
  const [selectedTab, setSelectedTab] = useState('active');
  const [isLoading, setIsLoading] = useState(false);
  const [bookingData, setBookingData] = useState([]);

  async function myBookings(props) {
    setIsLoading(true);
    try {
      const result = await getOrders('beauty');
      console.log('sdckndslkcnldsk', result);
      if (result.status == 200) {
        setBookingData(result.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    myBookings();
  }, []);

  useEffect(() => {
    Animated.timing(headerHeight, {
      toValue: 160,
      duration: 1000,
      useNativeDriver: false,
    }).start();
  }, []);
  return (
    <View style={{flex: 1, backgroundColor: ''}}>
      <StatusBar
        translucent={true}
        backgroundColor={'transparent'}
        barStyle={'dark-content'}
      />
      <Loader isActive={isLoading} />
      <HeaderComp
        title={'My Bookings'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View
        style={{
          flexDirection: 'row',
          marginTop: 20,
          marginHorizontal: 20,
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <TouchableOpacity
          onPress={() => setSelectedTab('active')}
          style={{
            backgroundColor: selectedTab === 'active' ? '#000000' : '#FFFFFF',
            borderRadius: 40,
            borderWidth: selectedTab === 'active' ? 0 : 1,
            borderColor: '#CCCCCC',
            width: 161,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text
            style={{
              fontSize: 14,
              color: selectedTab === 'active' ? '#FFFFFF' : '#000000',
              fontFamily: designeSheet.QuicksandSemiBold,
              marginVertical: 10,
            }}>
            {'Active Booking'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          activeOpacity={1}
          style={{
            backgroundColor:
              selectedTab === 'completed' ? '#000000' : '#FFFFFF',
            borderRadius: 40,
            borderWidth: selectedTab === 'completed' ? 0 : 1,
            borderColor: '#CCCCCC',
            width: 161,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text
            style={{
              fontSize: 14,
              color: selectedTab === 'completed' ? '#FFFFFF' : '#000000',
              fontFamily: designeSheet.QuicksandSemiBold,
              marginVertical: 10,
            }}>
            {'Completed'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView>
        {selectedTab == 'active' ? (
          bookingData.map((item, index) => (
            <TouchableOpacity
              activeOpacity={0.5}
              onPress={() => {
                props.navigation.navigate('BookingDetail');
              }}
              style={{
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 1},
                shadowOpacity: 0.4,
                shadowRadius: 10,
                elevation: 3,
                backgroundColor: 'white',
                marginHorizontal: 20,
                marginTop: 20,
                borderRadius: 12,
              }}>
              <View
                style={{
                  marginHorizontal: 20,
                  flexDirection: 'row',
                  marginTop: 10,
                  alignItems: 'center',
                }}>
                <View style={{flex: 1, flexDirection: 'row'}}>
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#6C6C6C',
                      fontFamily: designeSheet.QuicksandMedium,
                    }}>
                    {/* {'May 22, 2025 | 10:00 AM - 03:00Pm'} */}
                    {moment(item.bookingDate).format('MMM DD, YYYY')}
                  </Text>
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#6C6C6C',
                      fontFamily: designeSheet.QuicksandMedium,
                    }}>
                    {/* {'May 22, 2025 | 10:00 AM - 03:00Pm'} */}
                    {' | '}
                    {moment(item.startTime, 'HH:mm').format('hh: mm')}
                  </Text>
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#6C6C6C',
                      fontFamily: designeSheet.QuicksandMedium,
                    }}>
                    {/* {'May 22, 2025 | 10:00 AM - 03:00Pm'} */}
                    {' - '}
                    {moment(item.endTime, 'HH:mm').format('hh: mm')}
                  </Text>
                </View>

                <View
                  style={{flexDirection: 'row', gap: 6, alignItems: 'center'}}>
                  <Image
                    source={ImagePath.confirm}
                    style={{height: 15, width: 15}}
                  />
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#11983E',
                      fontFamily: designeSheet.QuicksandSemiBold,
                    }}>
                    {item.orderStatus}
                  </Text>
                </View>
              </View>
              <View
                style={{borderWidth: 1, borderColor: '#CCCCCC', marginTop: 10}}
              />
              <Text
                style={{
                  fontSize: 18,
                  color: '#1C1C28',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginHorizontal: 20,
                  marginTop: 10,
                }}>
                {'Salon At Home'}
              </Text>
              {item.items.map((v, i) => (
                <Text
                  style={{
                    fontSize: 14,
                    color: '#6C6C6C',
                    fontFamily: designeSheet.QuicksandMedium,
                    marginHorizontal: 20,
                  }}>
                  {v.itemRef.name}
                </Text>
              ))}
              {item.assignedBeautician == undefined ? null : (
                <View
                  style={{
                    flexDirection: 'row',
                    marginHorizontal: 20,
                    marginTop: 20,
                    alignItems: 'center',
                  }}>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Image
                      source={ImagePath.bookinggirl}
                      style={{height: 42, width: 42}}
                    />
                    <View style={{marginHorizontal: 10}}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontFamily: designeSheet.QuicksandMedium,
                          color: '#000000',
                        }}>
                        {'Riya Singh'}
                      </Text>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          gap: 3,
                        }}>
                        <Image
                          source={ImagePath.star}
                          style={{height: 11, width: 11}}
                        />
                        <Text
                          style={{
                            fontSize: 12,
                            fontFamily: designeSheet.QuicksandRegular,
                            color: '#000000',
                          }}>
                          {'4.8 ratings'}
                        </Text>
                      </View>
                    </View>
                  </View>
                  {/* <View style={{alignItems: 'flex-end'}}>
                <Text
                  style={{
                    fontSize: 14,
                    color: '#6C6C6C',
                    fontFamily: designeSheet.QuicksandMedium,
                  }}>
                  {'Start OTP'}
                </Text>
                <Text
                  style={{
                    fontSize: 16,
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandMedium,
                    textDecorationLine: 'underline',
                  }}>
                  {'7689'}
                </Text>
              </View> */}
                </View>
              )}
              <View
                style={{
                  flexDirection: 'row',
                  marginVertical: 20,
                  marginHorizontal: 20,
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setHelp(true);
                  }}
                  style={{backgroundColor: '#000000', borderRadius: 40}}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: '#FFFFFF',
                      fontFamily: designeSheet.QuicksandSemiBold,
                      marginHorizontal: 32,
                      marginVertical: 8,
                    }}>
                    {'Help'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  disabled={item.assignedBeautician == undefined}
                  onPress={() => {
                    props.navigation.navigate('Chat');
                  }}
                  style={{
                    borderRadius: 40,
                    borderWidth: 1,
                    borderColor: '#CCCCCC',
                    opacity: item.assignedBeautician == undefined ? 0.5 : 1,
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: '#000000',
                      fontFamily: designeSheet.QuicksandSemiBold,
                      marginHorizontal: 32,

                      marginVertical: 8,
                    }}>
                    {'Chat'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={async () => {
                    var obj = {
                      receiverId: '687a362796d4e2284b8e4027',
                    };
                    try {
                      const result = await startCall(obj);
                      if (result.status == 200) {
                        const callSession = sessionModel(result.data);
                        Global.callData = callSession;
                        Global.callStatus = 'waiting';
                        console.log(Global.callData);
                        props.navigation.navigate('CallPickupView');
                      }
                      console.log('RESPONSE', result);
                    } catch (error) {
                      console.error(error);
                    }
                  }}
                  // disabled={item.assignedBeautician == undefined}
                  style={{
                    borderRadius: 40,
                    borderWidth: 1,
                    borderColor: '#CCCCCC',
                    opacity: item.assignedBeautician == undefined ? 0.5 : 1,
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: '#000000',
                      fontFamily: designeSheet.QuicksandSemiBold,
                      marginHorizontal: 32,

                      marginVertical: 8,
                    }}>
                    {'Call'}
                  </Text>
                </TouchableOpacity>
              </View>
              {/* <View style={{marginTop: 10, marginBottom: 10}}>
            {status.map((item, index) => (
              <View key={index} style={{}}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginHorizontal: 20,
                  }}>
                  <Image source={item.img} style={{height: 18, width: 18}} />
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#000000',
                      marginHorizontal: 5,
                    }}>
                    {item.text}
                  </Text>
                </View>
                {index == 4 ? null : (
                  <View
                    style={{
                      borderWidth: 1,
                      height: 20,
                      width: 0,
                      marginHorizontal: 28,
                      marginTop: 3,
                      marginBottom: 3,
                      borderColor:
                        index == 0 || index == 1 ? '#11983E' : '#CCCCCC',
                      borderStyle: index == 0 || index == 1 ? '' : 'dotted',
                    }}
                  />
                )}
              </View>
            ))}
          </View> */}
            </TouchableOpacity>
          ))
        ) : (
          <View
            style={{
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 1},
              shadowOpacity: 0.4,
              shadowRadius: 10,
              elevation: 3,
              backgroundColor: 'white',

              marginHorizontal: 20,
              marginTop: 20,
              borderRadius: 12,
            }}>
            <View
              style={{
                marginHorizontal: 10,
                flexDirection: 'row',
                marginTop: 10,
                alignItems: 'center',
              }}>
              <View style={{flex: 1}}>
                <Text
                  style={{
                    fontSize: 12,
                    color: '#6C6C6C',
                    fontFamily: designeSheet.QuicksandMedium,
                  }}>
                  {'May 22, 2025 | 10:00 AM - 03:00Pm'}
                </Text>
              </View>

              <View
                style={{flexDirection: 'row', gap: 6, alignItems: 'center'}}>
                <Image
                  source={ImagePath.confirm}
                  style={{height: 15, width: 15}}
                />
                <Text
                  style={{
                    fontSize: 12,
                    color: '#11983E',
                    fontFamily: designeSheet.QuicksandSemiBold,
                  }}>
                  {'Confirmed'}
                </Text>
              </View>
            </View>
            <View
              style={{borderWidth: 0.5, borderColor: '#CCCCCC', marginTop: 10}}
            />
            <Text
              style={{
                fontSize: 18,
                color: '#1C1C28',
                fontFamily: designeSheet.QuicksandSemiBold,
                marginHorizontal: 10,
                marginTop: 10,
              }}>
              {'Salon At Home'}
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: '#6C6C6C',
                fontFamily: designeSheet.QuicksandMedium,
                marginHorizontal: 10,
              }}>
              {'Eyebrows , upper lips , underarms, chin, female disposable'}
            </Text>

            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginHorizontal: 10,
                marginVertical: 15,
              }}>
              <Image
                source={ImagePath.bookinggirl}
                style={{height: 42, width: 42}}
              />
              <View style={{marginHorizontal: 10}}>
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#000000',
                  }}>
                  {'Riya Singh'}
                </Text>
                <View
                  style={{flexDirection: 'row', alignItems: 'center', gap: 3}}>
                  <Image
                    source={ImagePath.star}
                    style={{height: 11, width: 11}}
                  />
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandRegular,
                      color: '#000000',
                    }}>
                    {'4.8 ratings'}
                  </Text>
                </View>
              </View>
            </View>

            <View
              style={{
                flexDirection: 'row',
                marginTop: 19,
                marginHorizontal: 10,
                alignSelf: 'center',
                alignItems: 'center',
                gap: 10,
                marginBottom: 20,
              }}>
              <TouchableOpacity
                onPress={() => {
                  props.navigation.navigate('ReviewScreen');
                }}
                style={{
                  backgroundColor: '#000000',
                  borderRadius: 40,

                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    fontSize: 14,
                    color: '#FFFFFF',
                    fontFamily: designeSheet.QuicksandSemiBold,
                    marginVertical: 10,
                    marginHorizontal: 30,
                  }}>
                  {'Write Review'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  borderRadius: 40,
                  borderWidth: 1,
                  borderColor: '#CCCCCC',

                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    fontSize: 14,
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandSemiBold,
                    marginVertical: 10,
                    marginHorizontal: 30,
                  }}>
                  {'Re- Schedule'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
      {help ? (
        <View
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            position: 'absolute',
            height: '100%',
            width: '100%',
            justifyContent: 'flex-end',
            gap: 20,
          }}>
          <Animated.View
            style={{
              height: headerHeight,
              width: '100%',
              backgroundColor: 'white',
              borderTopLeftRadius: 40,
              borderTopRightRadius: 40,
            }}>
            <ScrollView showsVerticalScrollIndicator={false} style={{}}>
              <TouchableOpacity
                onPress={() => {
                  Animated.timing(headerHeight, {
                    toValue: 0,
                    duration: 400,
                    useNativeDriver: false,
                  }).start(() => {
                    setHelp(false);
                  });
                }}
                style={{
                  borderWidth: 1,
                  borderColor: '#CCCCCCCC',
                  borderRadius: 30,
                  alignItems: 'center',
                  marginTop: 20,
                  marginHorizontal: 20,
                }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#6C6C6C',
                    marginVertical: 15,
                  }}>
                  {'Re-schedule'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  Animated.timing(headerHeight, {
                    toValue: 0,
                    duration: 400,
                    useNativeDriver: false,
                  }).start(() => {
                    setHelp(false);
                  });
                }}
                style={{
                  borderWidth: 1,
                  borderColor: '#CCCCCCCC',
                  borderRadius: 30,
                  alignItems: 'center',
                  marginTop: 20,
                  marginHorizontal: 20,
                }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#FF0000',
                    marginVertical: 15,
                  }}>
                  {'Cancel Booking'}
                </Text>
              </TouchableOpacity>
            </ScrollView>
          </Animated.View>
        </View>
      ) : null}
    </View>
  );
};

export default MyBooking;

const styles = StyleSheet.create({});
