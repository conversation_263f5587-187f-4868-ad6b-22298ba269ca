import React, {useRef, useEffect} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Animated,
  Dimensions,
  PanResponder,
  Alert,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import ImagePath from '../../../Assets/ImagePath/ImagePath';
import Global from '../../../Globals/Global';

const SWIPE_THRESHOLD = -60;
const MAX_SWIPE_HEIGHT = -100;

const CallPickupView = ({
  callerImage,
  callerName = 'Unknown',
  callerSubtitle,
  onAccept,
  onDecline,
}) => {
  const slideAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const dragY = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(slideAnim, {
          toValue: -10,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  }, [slideAnim]);

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.15,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  }, [pulseAnim]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gesture) => Math.abs(gesture.dy) > 5,
      onPanResponderMove: (_, gesture) => {
        if (gesture.dy < 0) {
          const clampedDy = Math.max(gesture.dy, MAX_SWIPE_HEIGHT);
          dragY.setValue(clampedDy);
        }
      },
      onPanResponderRelease: (_, gesture) => {
        if (gesture.dy < SWIPE_THRESHOLD) {
          Animated.timing(dragY, {
            toValue: MAX_SWIPE_HEIGHT,
            duration: 150,
            useNativeDriver: true,
          }).start(() => {
            Alert.alert('PICKED');
            onAccept?.();
            dragY.setValue(0);
          });
        } else {
          Animated.spring(dragY, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    }),
  ).current;

  const joinChannel = async () => {
    console.log('📞 Attempting to join channel...');
    console.log('Using channel:', CHANNEL_NAME);

    try {
      const result = engineRef.current?.joinChannel(
        TOKEN, // token
        CHANNEL_NAME, // channelId
        UID, // uid
        {
          clientRoleType: ClientRoleType.ClientRoleBroadcaster,
        },
      );

      console.log('✅ joinChannel() result:', result);
    } catch (error) {
      console.error('❌ joinChannel() error:', error);
    }
  };

  return (
    <LinearGradient
      colors={['#FFDEE9', '#B5FFFC']}
      style={{
        flex: 1,
        alignItems: 'center',
        paddingTop: 60,
        paddingHorizontal: 20,
        position: 'absolute',
        height: '100%',
        width: '100%',
      }}>
      {/* Logo */}
      <Image
        source={ImagePath.madamglogo}
        style={{
          width: Dimensions.get('screen').width / 2,
          resizeMode: 'contain',
          marginBottom: 30,
        }}
      />

      {/* Avatar with glow */}
      <Animated.View
        style={{
          borderRadius: 100,
          padding: 5,
          backgroundColor: 'rgba(255, 182, 193, 0.3)',
          transform: [{scale: pulseAnim}],
        }}>
        {callerImage ? (
          <Image
            source={
              typeof callerImage === 'string' ? {uri: callerImage} : callerImage
            }
            style={{
              width: 140,
              height: 140,
              borderRadius: 70,
              borderWidth: 3,
              borderColor: '#FFB6C1',
            }}
          />
        ) : (
          <View
            style={{
              width: 140,
              height: 140,
              borderRadius: 70,
              backgroundColor: '#FFD6E0',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text
              style={{
                fontSize: 48,
                fontWeight: 'bold',
                color: '#000',
              }}>
              {(callerName || 'U').charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </Animated.View>

      {/* Caller Info */}
      <Text
        style={{
          fontSize: 26,
          fontWeight: 'bold',
          color: '#222',
          marginTop: 20,
        }}>
        {callerName}
      </Text>
      <Text
        style={{
          fontSize: 16,
          color: '#555',
          marginTop: 5,
        }}>
        {callerSubtitle}
      </Text>

      {/* Buttons */}
      <View
        style={{
          position: 'absolute',
          bottom: 40,
          flexDirection: 'row',
          justifyContent: 'space-around',
          width: '100%',
          gap: 40,
        }}>
        <View style={{alignItems: 'center'}}>
          {/* Decline */}
          <TouchableOpacity
            style={{
              width: 75,
              height: 75,
              borderRadius: 37.5,
              alignItems: 'center',
              justifyContent: 'center',
              shadowColor: '#000',
              shadowOpacity: 0.2,
              shadowOffset: {width: 0, height: 3},
              shadowRadius: 5,
              elevation: 6,
              backgroundColor: '#F44336',
            }}
            onPress={onDecline}>
            <Image
              style={{height: 30, width: 30, tintColor: 'white'}}
              source={{
                uri: 'https://cdn-icons-png.flaticon.com/128/5604/5604556.png',
              }}
            />
          </TouchableOpacity>
          <Text
            style={{
              textAlign: 'left',
              color: '#444',
              marginTop: 8,
              fontSize: 12,
            }}>
            {Global.callStatus == 'waiting' ? 'Hang up' : 'Tap to decline'}
          </Text>
        </View>

        {/* Accept (swipe up) */}
        {Global.callStatus == 'waiting' ? null : (
          <Animated.View
            {...panResponder.panHandlers}
            style={{
              alignItems: 'center',
              transform: [{translateY: Animated.add(slideAnim, dragY)}],
            }}>
            <TouchableOpacity
              activeOpacity={0.8}
              style={{
                width: 75,
                height: 75,
                borderRadius: 37.5,
                alignItems: 'center',
                justifyContent: 'center',
                shadowColor: '#000',
                shadowOpacity: 0.2,
                shadowOffset: {width: 0, height: 3},
                shadowRadius: 5,
                elevation: 6,
                backgroundColor: '#4CAF50',
              }}>
              <Image
                style={{height: 25, width: 25, tintColor: 'white'}}
                source={{
                  uri: 'https://cdn-icons-png.flaticon.com/128/597/597177.png',
                }}
              />
            </TouchableOpacity>
            <Text
              style={{
                textAlign: 'left',
                color: '#444',
                marginTop: 8,
                fontSize: 12,
              }}>
              {'Swipe up to answer'}
            </Text>
          </Animated.View>
        )}
      </View>
    </LinearGradient>
  );
};

export default CallPickupView;
