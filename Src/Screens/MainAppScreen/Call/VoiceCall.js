import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  Button,
  Platform,
  PermissionsAndroid,
  Alert,
} from 'react-native';
import {
  createAgoraRtcEngine,
  ChannelProfileType,
  ClientRoleType,
  RtcSurfaceView,
} from 'react-native-agora';

const APP_ID = '66bba11633ec4fc993ee6cda1450dfb3';
const CHANNEL_NAME = 'testchannel';
const UID = 0;

const VoiceCall = () => {
  const engineRef = useRef(null);
  const [joined, setJoined] = useState(false);
  const [remoteUser, setRemoteUser] = useState(null);

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
      ]);
    }
  };

  useEffect(() => {
    const init = async () => {
      await requestPermissions();

      const engine = createAgoraRtcEngine();
      engineRef.current = engine;

      engine.initialize({
        appId: APP_ID,
        channelProfile: ChannelProfileType.ChannelProfileCommunication,
      });

      engine.enableAudio();
      console.log('🎤 Audio enabled');

      // ✅ Register events
      engine.registerEventHandler({
        onJoinChannelSuccess: (connection, elapsed) => {
          console.log('✅ onJoinChannelSuccess:', connection);
          setJoined(true);
        },
        onUserJoined: (connection, remoteUid) => {
          console.log('👤 Remote user joined:', remoteUid);
          setRemoteUser(remoteUid);
        },
        onUserOffline: (connection, remoteUid) => {
          console.log('❌ Remote user left:', remoteUid);
          setRemoteUser(null);
        },
        onError: err => {
          console.error('❌ Agora Error:', err);
        },
      });

      console.log('🚀 Agora engine initialized');
    };

    init();

    return () => {
      engineRef.current?.leaveChannel();
      engineRef.current?.release();
    };
  }, []);

  const joinChannel = async () => {
    console.log('📞 Attempting to join channel...');
    console.log('Using channel:', CHANNEL_NAME);

    try {
      const result = engineRef.current?.joinChannel(
        null, // token
        CHANNEL_NAME, // channelId
        UID, // uid
        {
          clientRoleType: ClientRoleType.ClientRoleBroadcaster,
        },
      );

      console.log('✅ joinChannel() result:', result);
    } catch (error) {
      console.error('❌ joinChannel() error:', error);
    }
  };

  const leaveChannel = async () => {
    try {
      await engineRef.current?.leaveChannel();
      setJoined(false);
      setRemoteUser(null);
      console.log('📴 Left the channel');
    } catch (error) {
      console.error('❌ leaveChannel() error:', error);
    }
  };

  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <Text style={{fontSize: 18, marginBottom: 10}}>
        {joined
          ? `✅ In call. Remote: ${remoteUser || 'Waiting...'}`
          : '📞 Not in call'}
      </Text>

      {joined ? (
        <Button title="Leave Call" onPress={leaveChannel} />
      ) : (
        <Button title="Join Call" onPress={joinChannel} />
      )}
    </View>
  );
};

export default VoiceCall;
