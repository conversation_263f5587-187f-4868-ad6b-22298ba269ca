import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const MyOrder = props => {
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'My Orders'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View
        style={{
          borderWidth: 1,
          borderColor: '#CCCCCCCC',
          marginTop: 24,
          flexDirection: 'row',
        }}>
        <Text
          style={{
            fontSize: 14,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#6C6C6C',
            marginHorizontal: 20,
            marginVertical: 15,
            flex: 1,
          }}>
          {'Showing all orders'}
        </Text>
        <View style={{borderWidth: 1, borderColor: '#CCCCCCCC'}} />
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Image
            source={ImagePath.filter}
            style={{height: 18, width: 18, marginHorizontal: 10}}
          />
          <Text
            style={{
              fontSize: 12,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#6C6C6C',
            }}>
            {'Filter      '}
          </Text>
        </View>
      </View>
      <View
        style={{
          marginTop: 24,
          gap: 15,
        }}>
        {[0, 0, 0].map((item, index) => (
          <TouchableOpacity
            onPress={() => {
              props.navigation.navigate('MyOrderDetail');
            }}
            key={index}
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCC',
              borderRadius: 8,
              marginHorizontal: 20,
              backgroundColor: '#F0F0F0',
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginHorizontal: 10,
                marginVertical: 10,
              }}>
              <Image
                source={ImagePath.ordres}
                style={{width: 17, height: 17}}
              />
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#6C6C6C',
                }}>
                {'  Order Delivered on 25-06-2025'}
              </Text>
            </View>
            <View style={{borderWidth: 1, borderColor: '#CCCCCC'}} />
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 5,
                marginHorizontal: 5,
                marginBottom: 6,
              }}>
              <Image source={ImagePath.fc} style={{height: 95, width: 98}} />
              <View style={{marginHorizontal: 14}}>
                <Text
                  style={{
                    fontSize: 16,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#1C1C28',
                  }}>
                  {'Vitamin C Face Serum'}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#6C6C6C',
                  }}>
                  {'Order ID: 89FD98D8'}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#6C6C6C',
                  }}>
                  {'Order Date: 20-06-2025'}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default MyOrder;

const styles = StyleSheet.create({});
