import {Image, ScrollView, StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const BookingDetail = props => {
  const [serviceAdded, setServiceAdded] = useState([{}, {}, {}, {}, {}]);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Booking Details'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <ScrollView>
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCC',
            marginHorizontal: 20,
            marginTop: 20,
            borderRadius: 12,
          }}>
          <View
            style={{
              marginHorizontal: 10,
              flexDirection: 'row',
              marginTop: 10,
              alignItems: 'center',
            }}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  fontSize: 12,
                  color: '#6C6C6C',
                  fontFamily: designeSheet.QuicksandMedium,
                }}>
                {'May 22, 2025 | 10:00 AM - 03:00Pm'}
              </Text>
            </View>

            <View style={{flexDirection: 'row', gap: 6, alignItems: 'center'}}>
              <Image
                source={ImagePath.confirm}
                style={{height: 15, width: 15}}
              />
              <Text
                style={{
                  fontSize: 12,
                  color: '#11983E',
                  fontFamily: designeSheet.QuicksandSemiBold,
                }}>
                {'Confirmed'}
              </Text>
            </View>
          </View>
          <View
            style={{borderWidth: 1, borderColor: '#CCCCCC', marginTop: 10}}
          />
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 10,
            }}>
            <Text
              style={{
                flex: 1,
                fontSize: 18,
                color: '#1C1C28',
                fontFamily: designeSheet.QuicksandSemiBold,

                marginTop: 10,
              }}>
              {'Salon At Home'}
            </Text>
            <Image
              source={ImagePath.homeimg1}
              style={{height: 16, width: 16}}
            />
          </View>

          <Text
            style={{
              fontSize: 14,
              color: '#6C6C6C',
              fontFamily: designeSheet.QuicksandMedium,
              marginHorizontal: 10,
            }}>
            {'Eyebrows , upper lips , underarms, chin, female disposable'}
          </Text>

          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 10,
              marginVertical: 15,
            }}>
            <Image
              source={ImagePath.bookinggirl}
              style={{height: 42, width: 42}}
            />
            <View style={{marginHorizontal: 10}}>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                }}>
                {'Riya Singh'}
              </Text>
              <View
                style={{flexDirection: 'row', alignItems: 'center', gap: 3}}>
                <Image
                  source={ImagePath.star}
                  style={{height: 11, width: 11}}
                />
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandRegular,
                    color: '#000000',
                  }}>
                  {'4.8 ratings'}
                </Text>
              </View>
            </View>
          </View>
        </View>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#000000',
            marginTop: 10,
            marginHorizontal: 20,
          }}>
          {'Services Added'}
        </Text>
        {serviceAdded.map((item, index) => (
          <View
            key={index}
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCC',
              borderRadius: 12,
              marginHorizontal: 20,
              marginTop: 10,
            }}>
            <View
              style={{
                flexDirection: 'row',
                marginHorizontal: 10,
                marginVertical: 10,
              }}>
              <Image source={ImagePath.book1} style={{height: 96, width: 75}} />
              <View style={{flex: 1, marginHorizontal: 5}}>
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#000000',
                  }}>
                  {'Facial Special X 1'}
                </Text>

                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#6C6C6C',
                    marginTop: 5,
                  }}>
                  {'Eyebrows , upper lops , underarms, chin, female disposable'}
                </Text>
                <Text
                  style={{
                    fontSize: 13,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#000000',
                    marginTop: 5,
                  }}>
                  {'₹899'}

                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#6C6C6C',
                    }}></Text>
                  {' | 1 hr 15 min'}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#E92E89',
                    textDecorationLine: 'underline',
                    marginTop: 5,
                  }}>
                  {'View Product'}
                </Text>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default BookingDetail;

const styles = StyleSheet.create({});
