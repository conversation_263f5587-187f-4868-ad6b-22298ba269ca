import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';

const WellNess = props => {
  const [doctor, setDoctor] = useState([
    {
      Image: ImagePath.w1,
    },
    {
      Image: ImagePath.w2,
    },
    {
      Image: ImagePath.w1,
    },
    {
      Image: ImagePath.w2,
    },
    {
      Image: ImagePath.w1,
    },
    {
      Image: ImagePath.w2,
    },
    {
      Image: ImagePath.w1,
    },
    {
      Image: ImagePath.w2,
    },
  ]);

  const [expert, setExpert] = useState([
    {
      image: ImagePath.w3,
      text: 'Ageing',
    },
    {
      image: ImagePath.w4,
      text: 'Hair Growth',
    },
    {
      image: ImagePath.w5,
      text: 'Eyes & Lips',
    },
    {
      image: ImagePath.w6,
      text: 'Hair Loss',
    },
    {
      image: ImagePath.w7,
      text: 'Skin',
    },
  ]);

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <ScrollView nestedScrollEnabled={true} style={{flex: 1}}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            marginHorizontal: 15,
            marginTop: 22,
          }}>
          <Image
            style={{height: 20.41, width: 16}}
            source={ImagePath.locationimg}
          />

          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <Text
              style={{
                marginHorizontal: 8,
                color: '#000000',
                fontFamily: designeSheet.QuicksandBold,
                fontSize: 14,
              }}>
              {'Chandigarh'}
            </Text>
            <Image style={{marginLeft: 5}} source={ImagePath.ArrowDown} />
          </View>
        </View>

        <Text
          style={{
            marginHorizontal: 8,
            color: '#000000',
            fontFamily: designeSheet.QuicksandBold,
            fontSize: 16,
            marginTop: 22,
            marginHorizontal: 15,
          }}>
          {'Doctors For Online Consultations'}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{flexDirection: 'row', marginHorizontal: 15, gap: 11}}>
            {doctor.map((item, index) => (
              <View
                key={index}
                style={{
                  borderWidth: 1,
                  borderColor: '#CCCCCC',
                  borderRadius: 12,
                  marginTop: 14,
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    marginHorizontal: 11,
                    marginVertical: 10,
                  }}>
                  <Image source={item.Image} style={{height: 82, width: 87}} />
                  <View style={{marginHorizontal: 20}}>
                    <Text
                      style={{
                        fontSize: 16,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#000000',
                      }}>
                      {'Dr. Prerna Sharma'}
                    </Text>
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandMedium,
                        color: '#6C6C6C',
                      }}>
                      {'HydraFacial Specialist'}
                    </Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: 10,
                      }}>
                      <Image
                        source={ImagePath.star}
                        style={{height: 12, width: 12}}
                      />
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: designeSheet.QuicksandRegular,
                          color: '#000000',
                        }}>
                        {'  4.8 ratings'}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
        <View
          style={{
            borderWidth: 0.6,
            borderColor: '#CCCCCCCC',
            marginHorizontal: 15,
            marginTop: 20,
          }}
        />
        <Text
          style={{
            marginHorizontal: 8,
            color: '#000000',
            fontFamily: designeSheet.QuicksandBold,
            fontSize: 18,
            marginTop: 15,
            marginHorizontal: 15,
          }}>
          {'Expert Care for Every Concern'}
        </Text>
        <View
          style={{
            flexDirection: 'row',
            marginHorizontal: 15,
            marginTop: 12,
            gap: 20,
          }}>
          {expert.map((item, index) => (
            <View key={index} style={{alignItems: 'center'}}>
              <Image
                source={item.image}
                style={{height: 51.51, width: 51.51}}
              />
              <Text
                style={{
                  fontSize: 12,
                  color: index == 0 ? '#E92E89' : '#000000',
                  fontFamily: designeSheet.QuicksandBold,
                  marginTop: 15,
                }}>
                {item.text}
              </Text>
            </View>
          ))}
        </View>
        <View
          style={{
            backgroundColor: '#5B7FDB',
            borderTopLeftRadius: 21,
            borderTopRightRadius: 21,
            height: 66,
            marginTop: 21,
            marginHorizontal: 15,
          }}></View>
        <View
          style={{
            backgroundColor: '#F7F6FA',
            borderBottomLeftRadius: 21,
            borderBottomRightRadius: 21,
            marginHorizontal: 15,
            alignItems: 'center',
          }}>
          <Image
            source={ImagePath.w8}
            style={{height: 62, width: 62, marginTop: -30}}
          />
          <Text
            style={{
              fontSize: 16,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#2A282F',
              marginTop: 6,
            }}>
            {'Ageing'}
          </Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandMedium,
              color: '#625F68',
              textAlign: 'center',
              marginBottom: 12,
              width: '80%',
            }}>
            {
              'Looking your best and staying youthful is a universal desire shared by people of all races, nationalities, and cultures.The Madam G Age Management Programme addresses sagging and loose skin, wrinkles, volume loss, and changes in skin texture on the face and neck. The programme combines targeted treatments with dietary guidance, as well as oral and topical medications for comprehensive results.'
            }
          </Text>
        </View>
        <View
          style={{
            backgroundColor: '#F7F6FA',
            borderRadius: 21,
            marginHorizontal: 15,
            marginTop: 21,
          }}>
          <View style={{marginHorizontal: 8, marginVertical: 8}}>
            <Text
              style={{
                color: '#2A282F',
                fontSize: 16,
                fontFamily: designeSheet.QuicksandBold,
              }}>
              {'Ageing Hands'}
            </Text>
            <Text
              style={{
                color: '#625F68',
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                marginTop: 8,
              }}>
              {
                'We spend fortunes on looking younger. But all of it becomes quite pointless if the hands give away the real story… and they always do. Not only are our hands vulnerable to the first signs of ageing, they often age faster than the face.'
              }
            </Text>
            <View
              style={{borderWidth: 1, borderColor: '#E2DEE9', marginTop: 15}}
            />

            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 12,
              }}>
              <Image source={ImagePath.w9} style={{height: 14, width: 16}} />
              <Text
                style={{
                  fontSize: 13,
                  fontFamily: designeSheet.QuicksandBold,
                  color: '#2A282F',
                }}>
                {'  Treatements'}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                marginTop: 8,
                gap: 10,
              }}>
              {[0, 0, 0, 0, 0].map((item, index) => (
                <View
                  key={index}
                  style={{
                    backgroundColor: '#000000',
                    borderRadius: 8,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#FFFFFF',
                      marginHorizontal: 12,
                      marginVertical: 5,
                    }}>
                    {'Clearlift Hands'}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => {
            props.navigation.navigate('BookConsult');
          }}
          style={{
            backgroundColor: '#000000',
            borderRadius: 8,
            padding: 10,
            alignItems: 'center',
            justifyContent: 'center#000000',
            marginHorizontal: 15,
            marginTop: 20,
            marginBottom: 20,
          }}>
          <Text
            style={{
              fontSize: 16,
              color: '#FFFFFF',
              fontFamily: designeSheet.QuicksandMedium,
            }}>
            {'Book Consultation'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default WellNess;

const styles = StyleSheet.create({});
