import {
  Alert,
  Animated,
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';
import {useDispatch} from 'react-redux';
import {setbottomvalue} from '../../Redux/CreatSlice';

const AddPackage = props => {
  const headerHeight = useRef(new Animated.Value(0)).current;
  const headerHeight1 = useRef(new Animated.Value(0)).current;
  const dispatch = useDispatch();
  const [paymentSum, setPaymentSum] = useState([
    {
      text: 'Item total',
      amount: '₹998',
    },
    {
      text: 'Tax & Fee',
      amount: '₹60',
    },
    {
      text: 'Transportation charges',
      amount: '₹100',
    },
    {
      text: 'Amount to pay',
      amount: '₹1158',
    },
  ]);
  const [bookingSomeoneElse, setBookingSomeoneElse] = useState(false);
  const [recipient, setRecipient] = useState('');
  const [infoUpdate, setInfoUpdate] = useState(false);
  useEffect(() => {
    Animated.timing(headerHeight, {
      toValue: 260,
      duration: 1000,
      useNativeDriver: false,
    }).start();
  }, []);
  useEffect(() => {
    Animated.timing(headerHeight1, {
      toValue: 200,
      duration: 1000,
      useNativeDriver: false,
    }).start();
  }, []);
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#FFF7FB',
      }}>
      <HeaderComp
        title={'Summary'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <ScrollView>
        {infoUpdate ? (
          <TouchableOpacity
            onPress={() => {
              setBookingSomeoneElse(true);
              setInfoUpdate(true);
            }}
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCC',
              borderRadius: 12,
              marginHorizontal: 20,
              padding: 10,
              marginTop: 20,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginHorizontal: 10,
              }}>
              <Image
                source={ImagePath.giftbox}
                style={{
                  height: 22,
                  width: 21,
                }}
              />
              <Text
                style={{
                  flex: 1,
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                  marginHorizontal: 10,
                }}>
                {'Booking for Someone else?'}
              </Text>

              <Text
                style={{
                  color: '#E92E89',
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginVertical: 2,
                  marginHorizontal: 10,
                }}>
                {'CHANGE'}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginHorizontal: 10,
              }}>
              <Image
                source={ImagePath.sum10}
                style={{
                  height: 16,
                  width: 16,
                }}
              />
              <View
                style={{
                  flex: 1,
                  marginHorizontal: 10,
                }}>
                <Text
                  style={{
                    color: '#6C6C6C',
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandSemiBold,
                  }}>
                  {'Recipient’s details'}
                </Text>
                <Text
                  style={{
                    color: '#6C6C6C',
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                  }}>
                  {'Riya , 8989898989'}
                </Text>
              </View>
              <Image
                source={ImagePath.sum11}
                style={{
                  height: 18,
                  width: 18,
                  marginHorizontal: 10,
                }}
              />
            </View>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            onPress={() => {
              setBookingSomeoneElse(true);
            }}
            style={{
              borderWidth: 1,
              borderColor: '#CCCCCC',
              borderRadius: 12,
              marginHorizontal: 20,
              padding: 10,
              marginTop: 20,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginHorizontal: 10,
              }}>
              <Image
                source={ImagePath.giftbox}
                style={{height: 22, width: 21}}
              />
              <Text
                style={{
                  flex: 1,
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#000000',
                  marginHorizontal: 10,
                }}>
                {'Booking for Someone else?'}
              </Text>
              <View
                style={{
                  borderWidth: 1,
                  borderColor: '#E92E89',
                  borderRadius: 6,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    color: '#E92E89',
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    marginVertical: 2,
                    marginHorizontal: 10,
                  }}>
                  {'ADD'}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        )}
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 12,
            marginHorizontal: 20,
            padding: 10,
            marginTop: 20,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 10,
            }}>
            <View style={{flex: 1}}>
              <Text
                style={{
                  fontSize: 16,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#000000',
                }}>
                {'Korean Glow Facial'}
              </Text>
              <Text
                style={{
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandMedium,
                  color: '#6C6C6C',
                }}>
                {'10 steps Facial | Include Free silicon facial brush'}
              </Text>
              <Text
                style={{
                  fontSize: 13,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandMedium,
                  marginTop: 5,
                }}>
                {'₹899'}
                <Text
                  style={{
                    fontSize: 12,
                    color: '#6C6C6C',
                    fontFamily: designeSheet.QuicksandMedium,
                  }}>
                  {' | 1 hr 15 min'}
                </Text>
              </Text>
            </View>

            <View
              style={{
                borderWidth: 1,
                borderColor: '#E92E89',
                borderRadius: 6,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  color: '#E92E89',
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginVertical: 2,
                  marginHorizontal: 10,
                }}>
                {'-  1  +'}
              </Text>
            </View>
          </View>
          <View
            style={{
              borderWidth: 0.5,
              borderColor: '#6C6C6CCC',
              marginTop: 10,
              marginHorizontal: 10,
            }}
          />
          <View style={{flexDirection: 'row'}}>
            <View>
              <Text
                style={{
                  fontSize: 13,
                  color: '#6C6C6C',
                  fontFamily: designeSheet.QuicksandRegular,
                  marginHorizontal: 10,
                  marginTop: 10,
                }}>
                {'Services Charges'}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginHorizontal: 10,
                  marginTop: 5,
                }}>
                {'₹299'}
              </Text>
            </View>
            <View>
              <Text
                style={{
                  fontSize: 13,
                  color: '#6C6C6C',
                  fontFamily: designeSheet.QuicksandRegular,
                  marginHorizontal: 10,
                  marginTop: 10,
                }}>
                {'Product Cost'}
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: '#000000',
                  fontFamily: designeSheet.QuicksandSemiBold,
                  marginHorizontal: 10,
                  marginTop: 5,
                }}>
                {'₹499'}
              </Text>
            </View>
          </View>
          <View
            style={{
              borderWidth: 0.5,
              borderColor: '#6C6C6CCC',
              marginTop: 10,
              marginHorizontal: 10,
            }}
          />
          <Text
            style={{
              fontSize: 14,
              color: '#000000',
              fontFamily: designeSheet.QuicksandSemiBold,
              marginTop: 10,
              marginHorizontal: 10,
            }}>
            {'. Korean Glow Pro Facial Kit x 1'}
          </Text>
        </View>
        <Text
          style={{
            fontSize: 18,
            color: '#000000',
            fontFamily: designeSheet.QuicksandSemiBold,
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'Last minute Add-on'}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 20,
            }}>
            {[0, 0].map((item, index) => (
              <View
                key={index}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginHorizontal: 8,
                  marginTop: 20,
                  borderWidth: 1,
                  borderColor: 'black',
                  borderRadius: 8,
                }}>
                <Image
                  source={ImagePath.serum}
                  style={{width: 93, height: 104}}
                />
                <View
                  style={{
                    marginHorizontal: 10,
                  }}>
                  <Text
                    style={{
                      fontSize: 18,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                    }}>
                    {'Combo Of Face seru...'}
                  </Text>
                  <Text
                    style={{
                      fontSize: 13,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#6C6C6C',
                      textDecorationLine: 'line-through',
                    }}>
                    {'₹499'}
                    <Text
                      style={{
                        fontSize: 13,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#6C6C6C',
                        textDecorationLine: '',
                      }}>
                      {'  ₹499 | '}
                      <Text
                        style={{
                          fontSize: 15,
                          fontFamily: designeSheet.QuicksandSemiBold,
                          color: '#2CAA3B',
                        }}>
                        {'50% off'}
                      </Text>
                    </Text>
                  </Text>
                  <View
                    style={{
                      backgroundColor: '#000000',
                      height: 24,
                      width: 60,
                      borderRadius: 4,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginTop: 10,
                    }}>
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 12,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        marginVertical: 2,
                      }}>
                      {'ADD'}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
        <Text
          style={{
            fontSize: 18,
            color: '#000000',
            fontFamily: designeSheet.QuicksandSemiBold,
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'People also availed'}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 10,
            }}>
            {[0, 0, 0].map((item, index) => (
              <View
                key={index}
                style={{
                  marginHorizontal: 10,
                  marginTop: 20,
                }}>
                <Image
                  source={ImagePath.sum1}
                  style={{height: 117, width: 124}}
                />
                <Text
                  style={{
                    fontSize: 13,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#000000',
                  }}>
                  {'Face Glow up at home'}
                </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <Image
                    source={ImagePath.clockimg}
                    style={{
                      height: 13,
                      width: 13,
                    }}
                  />
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#6C6C6C',
                      fontFamily: designeSheet.QuicksandMedium,
                      marginHorizontal: 5,
                    }}>
                    {'30 mins'}
                  </Text>
                </View>
                <Text
                  style={{
                    fontSize: 13,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#6C6C6C',
                    textDecorationLine: 'line-through',
                  }}>
                  {'₹499'}
                  <Text
                    style={{
                      fontSize: 13,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#6C6C6C',
                      textDecorationLine: '',
                    }}>
                    {'  ₹499 | '}
                    <Text
                      style={{
                        fontSize: 15,
                        fontFamily: designeSheet.QuicksandSemiBold,
                        color: '#2CAA3B',
                      }}>
                      {'50% off'}
                    </Text>
                  </Text>
                </Text>
                <View
                  style={{
                    marginTop: 5,
                    borderWidth: 1,
                    borderColor: '#E92E89',
                    borderRadius: 6,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      color: '#E92E89',
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      marginVertical: 2,
                      marginHorizontal: 10,
                    }}>
                    {'ADD'}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 8,
            marginHorizontal: 20,
            marginTop: 20,
          }}>
          <Text
            style={{
              fontSize: 18,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
              marginHorizontal: 20,
              marginTop: 10,
            }}>
            {'Choose Professional'}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 10,
              marginHorizontal: 20,
              gap: 20,
              marginBottom: 10,
            }}>
            <View
              style={{
                borderWidth: 1,
                borderColor: '#AC69F4',
                borderRadius: 13,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginHorizontal: 10,
                  marginVertical: 10,
                }}>
                <Image
                  source={ImagePath.sum4}
                  style={{
                    height: 35,
                    width: 35,
                  }}
                />
                <View style={{marginHorizontal: 10}}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#000000',
                    }}>
                    {'Standard'}
                  </Text>
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#1B5ABB',
                    }}>
                    {'₹7 Per min'}
                  </Text>
                </View>
              </View>
            </View>
            <View
              style={{
                borderWidth: 1,
                borderColor: '#CCCCCC',
                borderRadius: 13,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginHorizontal: 10,
                  marginVertical: 10,
                }}>
                <Image
                  source={ImagePath.sum4}
                  style={{
                    height: 35,
                    width: 35,
                  }}
                />
                <View
                  style={{
                    marginHorizontal: 10,
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#DEA948',
                    }}>
                    {'Gold'}
                  </Text>
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#1B5ABB',
                    }}>
                    {'₹12 Per min'}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
        <View
          style={{
            flex: 1,
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 8,
            marginHorizontal: 20,
            marginTop: 20,
          }}>
          <View
            style={{
              marginHorizontal: 20,
              marginTop: 10,
            }}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {'Select Date'}
            </Text>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                gap: 10,
                marginTop: 10,
              }}>
              {[0, 0, 0, 0, 0].map((item, index) => (
                <View
                  key={index}
                  style={{
                    borderWidth: 1,
                    borderColor: '#CCCCCC',
                    borderRadius: 12,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#6C6C6C',
                      marginHorizontal: 10,
                      marginVertical: 2,
                    }}>
                    {'Wed'}
                  </Text>
                  <Text
                    style={{
                      fontSize: 15,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#6C6C6C',
                      marginHorizontal: 10,
                    }}>
                    {'14'}
                  </Text>
                </View>
              ))}
            </View>
            <Text
              style={{
                fontSize: 14,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
                marginTop: 10,
              }}>
              {'Select Service Start Time'}
            </Text>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                gap: 10,
                marginTop: 10,
                flexWrap: 'wrap',
                marginBottom: 10,
              }}>
              {[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0].map((item, index) => (
                <View
                  key={index}
                  style={{
                    borderWidth: 1,
                    borderColor: '#CCCCCC',
                    borderRadius: 5,
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: 5,
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandSemiBold,
                      color: '#6C6C6C',
                      marginHorizontal: 10,
                      marginVertical: 5,
                    }}>
                    {'07:00 AM'}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>
        <View
          style={{
            flex: 1,
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 8,
            marginHorizontal: 20,
            marginTop: 20,
          }}>
          <View
            style={{
              marginHorizontal: 20,
              marginTop: 10,
            }}>
            <Text
              style={{
                fontSize: 16,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {'Discount & Coupons'}
            </Text>
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#000000',
              }}>
              {'You can only choose one of the below options'}
            </Text>
            <View
              style={{
                borderWidth: 1,
                borderColor: '#CCCCCC',
                borderRadius: 4,
                marginTop: 10,
                padding: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <Image
                  source={ImagePath.perimg}
                  style={{height: 18, width: 18}}
                />
                <Text
                  style={{
                    flex: 1,
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#6C6C6C',
                    marginHorizontal: 10,
                  }}>
                  {'Coupons and offers'}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    fontFamily: designeSheet.QuicksandMedium,
                    color: '#6C6C6C',
                    marginHorizontal: 10,
                  }}>
                  {'View offers'}
                </Text>
                <Image
                  source={ImagePath.ArrowDown}
                  style={{
                    height: 6,
                    width: 8,
                  }}
                />
              </View>
            </View>
            <View
              style={{
                borderWidth: 1,
                borderColor: '#CCCCCC',
                borderRadius: 4,
                marginTop: 10,
                padding: 10,
                marginBottom: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <Image
                  source={ImagePath.sum6}
                  style={{
                    height: 18,
                    width: 18,
                  }}
                />
                <Text
                  style={{
                    flex: 1,
                    fontSize: 14,
                    fontFamily: designeSheet.QuicksandSemiBold,
                    color: '#6C6C6C',
                    marginHorizontal: 10,
                  }}>
                  {'Madam G wallet'}
                </Text>

                <Image
                  source={ImagePath.sum8}
                  style={{height: 15, width: 15.5}}
                />
              </View>
            </View>
          </View>
        </View>
        <View
          style={{
            flex: 1,
            borderWidth: 1,
            borderColor: '#CCCCCC',
            borderRadius: 8,
            marginHorizontal: 20,
            marginTop: 20,
            marginBottom: Platform.OS == 'ios' ? 20 : 0,
          }}>
          <View
            style={{
              marginHorizontal: 20,
              marginTop: 10,
            }}>
            <Text
              style={{
                fontSize: 16,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {'Payment Summary'}
            </Text>
          </View>
          <View
            style={{
              borderWidth: 1,
              borderColor: '#E6E6E6',
              marginTop: 10,
            }}
          />
          {paymentSum.map((item, index) => (
            <View
              key={index}
              style={{
                marginHorizontal: 20,
                marginTop: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <View>
                  <Text
                    style={{
                      fontSize: 15,
                      color: '#000000',
                      fontFamily: designeSheet.QuicksandMedium,
                    }}>
                    {item.text}
                  </Text>
                  {index == 2 ? (
                    <Text
                      style={{
                        fontSize: 11,
                        color: '#6C6C6C',
                        fontFamily: designeSheet.QuicksandRegular,
                      }}>
                      {'100% of this goes to the professional'}
                    </Text>
                  ) : null}
                </View>
                <Text
                  style={{
                    fontSize: 16,
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandMedium,
                  }}>
                  {item.amount}
                </Text>
              </View>
              {index == 2 ? (
                <View
                  style={{
                    borderWidth: 1,
                    borderColor: '#E6E6E6',
                    marginTop: 10,
                  }}
                />
              ) : null}
            </View>
          ))}
          <Text
            style={{
              marginHorizontal: 20,
              marginTop: 20,
              fontSize: 14,
              fontFamily: designeSheet.QuicksandSemiBold,
              color: '#000000',
            }}>
            {'Add a tip to thank the Professional'}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 20,
              marginHorizontal: 20,
              marginTop: 10,
            }}>
            {[0, 0, 0].map((item, index) => (
              <View
                key={index}
                style={{
                  borderWidth: 1,
                  borderColor: '#E6E6E6',
                  borderRadius: 8,
                  padding: 10,
                }}>
                <Text
                  style={{
                    fontSize: 14,
                    color: '#000000',
                    fontFamily: designeSheet.QuicksandRegular,
                  }}>
                  {'₹40'}
                </Text>
              </View>
            ))}
          </View>
          <Text
            style={{
              marginHorizontal: 20,
              marginTop: 20,
              fontSize: 11,
              fontFamily: designeSheet.QuicksandRegular,
              color: '#000000',
              marginBottom: 10,
            }}>
            {'100% tip goes to the professional'}
          </Text>
          <TouchableOpacity
            onPress={() => {
              Alert.alert('Your Order Booked Successfully', '', [
                {
                  text: 'OK',
                  onPress: () => {
                    dispatch(setbottomvalue(0));
                    props.navigation.navigate('Home');
                  },
                },
              ]);
            }}
            style={{
              backgroundColor: '#000000',
              borderRadius: 8,
              padding: 10,
              alignItems: 'center',
              justifyContent: 'center#000000',
              marginHorizontal: 20,
              marginTop: 20,
              marginBottom: 20,
            }}>
            <Text
              style={{
                fontSize: 16,
                color: '#FFFFFF',
                fontFamily: designeSheet.QuicksandMedium,
              }}>
              {'Pay ₹1218 '}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      {bookingSomeoneElse ? (
        <View
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            position: 'absolute',
            height: '100%',
            width: '100%',
            justifyContent: 'flex-end',
            gap: 20,
          }}>
          <Animated.View
            style={{
              height: infoUpdate ? headerHeight1 : headerHeight,
              width: '100%',
              backgroundColor: 'white',
              borderTopLeftRadius: 40,
              borderTopRightRadius: 40,
            }}>
            <ScrollView showsVerticalScrollIndicator={false} style={{}}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <View style={{flex: 1}}>
                  <Text
                    style={{
                      fontSize: 18,
                      color: '#000000',
                      fontFamily: designeSheet.QuicksandBold,
                      marginHorizontal: 20,
                      marginTop: 20,
                    }}>
                    {'Booking for someone else '}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => {
                    Animated.timing(headerHeight, {
                      toValue: 0,
                      duration: 400,
                      useNativeDriver: false,
                    }).start(() => {
                      setBookingSomeoneElse(false);
                    });
                  }}>
                  <Image
                    source={ImagePath.crossimg}
                    style={{
                      height: 24,
                      width: 24,
                      marginTop: 20,
                      marginHorizontal: 20,
                    }}
                  />
                </TouchableOpacity>
              </View>
              <Text
                style={{
                  fontSize: 12,
                  color: '#6C6C6C',
                  fontFamily: designeSheet.QuicksandRegular,
                  marginHorizontal: 20,
                  marginTop: 10,
                }}>
                {'we will share booking details on recipient’s mobile number'}
              </Text>
              {infoUpdate ? (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginHorizontal: 20,
                    marginTop: 20,
                  }}>
                  <Image
                    source={ImagePath.yesCheck}
                    style={{
                      height: 18,
                      width: 18,
                      borderRadius: 50,
                    }}
                  />
                  <Text
                    style={{
                      fontSize: 16,
                      marginHorizontal: 5,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#000000',
                    }}>
                    {'Riya, 8989898989'}
                  </Text>
                </View>
              ) : null}

              {infoUpdate ? null : (
                <View
                  style={{
                    borderWidth: 1,
                    borderColor: '#CCCCCC',
                    borderRadius: 8,
                    marginHorizontal: 20,
                    marginTop: 15,
                  }}>
                  <TextInput
                    placeholder="  Add recipient’s name"
                    placeholderTextColor={'#6C6C6C'}
                    style={{
                      fontSize: 14,
                      color: '#6C6C6C',
                      fontFamily: designeSheet.QuicksandBold,
                    }}
                    value={recipient}
                    onChangeText={setRecipient}
                  />
                </View>
              )}
              {infoUpdate ? null : (
                <View
                  style={{
                    borderWidth: 1,
                    borderColor: '#CCCCCC',
                    borderRadius: 8,
                    marginHorizontal: 20,
                    marginTop: 15,
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <View style={{flex: 1}}>
                    <TextInput
                      placeholder="  Add recipient’s mobile number"
                      placeholderTextColor={'#6C6C6C'}
                      style={{
                        fontSize: 14,
                        color: '#6C6C6C',
                        fontFamily: designeSheet.QuicksandBold,
                      }}
                      value={recipient}
                      onChangeText={setRecipient}
                    />
                  </View>

                  <Image
                    source={ImagePath.sum9}
                    style={{
                      height: 18,
                      width: 18,
                      marginHorizontal: 10,
                    }}
                  />
                </View>
              )}
              {infoUpdate ? (
                <TouchableOpacity
                  onPress={() => {
                    setInfoUpdate(false);
                  }}
                  style={{
                    borderWidth: 1,
                    borderColor: '#E92E89',
                    borderRadius: 8,
                    padding: 10,
                    marginTop: 20,
                    marginHorizontal: 20,
                    alignItems: 'center',
                    justifyContent: 'center',
                    alignSelf: 'center',
                  }}>
                  <Text
                    style={{
                      color: '#E92E89',
                      fontSize: 16,
                      fontFamily: designeSheet.QuicksandSemiBold,
                    }}>
                    {'+ Add New Recipient'}
                  </Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    Animated.timing(headerHeight, {
                      toValue: 0,
                      duration: 400,
                      useNativeDriver: false,
                    }).start(() => {
                      setBookingSomeoneElse(false);
                      setInfoUpdate(true);
                    });
                  }}
                  style={{
                    backgroundColor: '#000000',
                    borderRadius: 8,
                    padding: 10,
                    alignItems: 'center',
                    justifyContent: 'center#000000',
                    marginHorizontal: 20,
                    marginTop: 20,
                    marginBottom: 20,
                  }}>
                  <Text
                    style={{
                      fontSize: 16,
                      color: '#FFFFFF',
                      fontFamily: designeSheet.QuicksandMedium,
                    }}>
                    {'Add Details'}
                  </Text>
                </TouchableOpacity>
              )}
            </ScrollView>
          </Animated.View>
        </View>
      ) : null}
    </View>
  );
};

export default AddPackage;

const styles = StyleSheet.create({});
