import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  ImageBackground,
  StatusBar,
  Platform,
  Animated,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import React, {useState, useEffect, useRef} from 'react';
import ImagePath from '../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../Designe/designeSheet';
import Global from '../../../Globals/Global';
import {getCategories} from "../../../Api's/Api";
import DynamicImage from '../../../components/DynamicImage';
import {IMAGE_BASE_URL} from '../../../utils/urls';
import Loader from '../../../components/Loader';
import CustomShimmer from '../../../components/CustomShimmer';
import {animateText, getCurrentLocation} from '../../../utils/CommonFunctions';
import StickyCartView from '../CategoryDetails/StickyCartView';
import Offers from './Cards/Offers';
import CustomerLovesUs from './Cards/CustomerLovesUs';
import MostBooked from './Cards/MostBooked';

const HomeScreen = ({props, setSelectAddressPopup, headerHeight1}) => {
  const [isLoading, setIsLoading] = useState(false);
  const keywords = ['scrub', 'massage', 'facial', 'therapy'];
  const [index, setIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;
  const [cartData, setCartData] = useState([]);

  const [everyThingData, setEveryThingData] = useState([]);

  async function homeApi() {
    setIsLoading(true);
    try {
      const result = await getCategories();
      if (result.status == 200) {
        setEveryThingData(result.data.data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    homeApi();
    getCartItmes();
    console.log('ACCESS tOKEN', Global.accesstoken);
    const interval = setInterval(() => {
      animateText(fadeAnim, translateYAnim, keywords, setIndex);
    }, 2500);
    return () => clearInterval(interval);
  }, []);

  const scrollY = useRef(new Animated.Value(0)).current;
  const HEADER_HEIGHT = 370;

  async function getCartItmes() {
    var type = Global.isGuestUser == 'true' ? 'guestId' : 'userId';
    var id = Global.isGuestUser == 'true' ? Global.guest_id : Global.user_id;
    try {
      var url = `${API_URL.getCartData}?${type}=${id}&category=beauty`;
      const result = await getCartItems(url);
      if (result.status == 200) {
        setCartData(result.data.items);
      }
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <StatusBar
        translucent={true}
        backgroundColor={'transparent'}
        barStyle={'dark-content'}
      />

      <View style={{backgroundColor: '#FFF7FB', flex: 1}}>
        <Animated.ScrollView
          refreshControl={
            <RefreshControl
              refreshing={false}
              onRefresh={() => {
                homeApi();
                getCurrentLocation();
              }}
            />
          }
          nestedScrollEnabled={true}
          style={{flex: 1, backgroundColor: '#FFF7FB'}}
          scrollEventThrottle={16}
          onScroll={Animated.event(
            [{nativeEvent: {contentOffset: {y: scrollY}}}],
            {useNativeDriver: true},
          )}
          contentContainerStyle={{paddingTop: HEADER_HEIGHT}}>
          <Animated.View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: HEADER_HEIGHT,
              transform: [
                {
                  translateY: scrollY.interpolate({
                    inputRange: [-HEADER_HEIGHT, 0, HEADER_HEIGHT],
                    outputRange: [-HEADER_HEIGHT / 2, 0, HEADER_HEIGHT * 0.75],
                  }),
                },
                {
                  scale: scrollY.interpolate({
                    inputRange: [-HEADER_HEIGHT, 0, HEADER_HEIGHT],
                    outputRange: [2, 1, 1],
                  }),
                },
              ],
            }}>
            <ImageBackground
              style={{
                flex: 1,
                height: 377,
                paddingTop:
                  Platform.OS == 'android' ? StatusBar.currentHeight : 40,
              }}
              source={ImagePath.BackGroundImage}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => {
                  if (Global.shortAddress == 'Unable to get location!') {
                    getCurrentLocation();
                  } else {
                    setSelectAddressPopup(true);
                    Animated.timing(headerHeight1, {
                      toValue: 600,
                      duration: 600,
                      useNativeDriver: false,
                    }).start();
                  }
                }}
                style={styles.addressView}>
                <View
                  style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                  <Image
                    style={{height: 20.41, width: 16}}
                    source={ImagePath.locationimg}
                  />
                  <View style={{flex: 0.8}}>
                    {Global.gettingLocation ? (
                      <ActivityIndicator
                        style={{
                          alignSelf: 'flex-start',
                          marginVertical: 10,
                          marginLeft: 10,
                        }}
                        size={15}
                        color={'black'}
                      />
                    ) : (
                      <View>
                        <View style={styles.addressTextView}>
                          <Text style={styles.addressMainText}>
                            {Global.shortAddress}
                          </Text>
                          <Image
                            style={{marginLeft: 5}}
                            source={ImagePath.ArrowDown}
                          />
                        </View>
                        <Text numberOfLines={1} style={styles.addressText}>
                          {Global.fullAddress}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate('ChooseCart');
                  }}>
                  <Image
                    source={ImagePath.cartimg}
                    style={{height: 20, width: 20}}
                  />
                </TouchableOpacity>
              </TouchableOpacity>
              <TouchableOpacity
                activeOpacity={1}
                style={{
                  backgroundColor: '#FFF7FB',
                  marginHorizontal: 20,
                  marginTop: 10,
                  borderRadius: 10,
                  flexDirection: 'row',
                  alignItems: 'center',
                  padding: 3,
                }}>
                <Image
                  source={ImagePath.searchimg}
                  style={{height: 19, width: 19, marginHorizontal: 10}}
                />
                <View style={{flexDirection: 'row', marginVertical: 10}}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: '#6C6C6C',
                      fontFamily: designeSheet.QuicksandBold,
                    }}>
                    Search for{' '}
                  </Text>
                  <Animated.Text
                    style={{
                      opacity: fadeAnim,
                      transform: [{translateY: translateYAnim}],
                      fontSize: 14,
                      color: '#6C6C6C',
                      fontFamily: designeSheet.QuicksandBold,
                    }}>
                    “{keywords[index]}”
                  </Animated.Text>
                </View>
              </TouchableOpacity>

              <View style={{marginTop: 50, justifyContent: 'space-around'}}>
                <Text
                  style={{
                    fontSize: 28,
                    marginLeft: 20,
                    fontFamily: designeSheet.QuicksandBold,
                    lineHeight: 36,
                    color: '#0A6351',
                  }}>
                  Keratin
                </Text>
                <Text
                  style={{
                    fontSize: 28,
                    marginLeft: 20,
                    fontFamily: designeSheet.QuicksandBold,
                    lineHeight: 36,
                    color: '#1C8A73',
                  }}>
                  Therapy
                </Text>
                <Text
                  style={{
                    fontSize: 16,
                    marginLeft: 20,
                    fontFamily: designeSheet.QuicksandMedium,
                    lineHeight: 36,
                  }}>
                  for Silky Hair
                </Text>
                <Text
                  style={{
                    marginTop: 15,
                    borderRadius: 5.12,
                    fontFamily: designeSheet.QuicksandBold,
                    backgroundColor: '#22A288',
                    alignSelf: 'flex-start',
                    marginLeft: 20,
                    color: 'white',
                    padding: 8,
                  }}>
                  Book Now
                </Text>
              </View>
            </ImageBackground>
          </Animated.View>

          <View style={{flex: 1, margin: 0, backgroundColor: '#FFF7FB'}}>
            <Text
              style={{
                color: '#000000',
                fontSize: 18,
                fontFamily: designeSheet.QuicksandBold,
                paddingLeft: 16,
              }}>
              Everything That You Need
            </Text>

            <FlatList
              numColumns={4}
              data={everyThingData}
              style={{marginVertical: 10}}
              contentContainerStyle={{gap: 10}}
              ListEmptyComponent={() => {
                return (
                  <View
                    style={{
                      flexDirection: 'row',
                      flexWrap: 'wrap',
                      paddingHorizontal: 16,
                    }}>
                    {[...Array(7)].map((_, i) => (
                      <View
                        key={i}
                        style={{
                          width: '25%',
                          marginBottom: 16,
                          alignItems: 'center',
                          gap: 5,
                        }}>
                        <View
                          style={{
                            height: 80,
                            width: '85%',
                            borderRadius: 12,
                            alignSelf: 'center',
                            justifyContent: 'center',
                            overflow: 'hidden',
                          }}>
                          <CustomShimmer height={89} width={89} />
                        </View>
                        <View
                          style={{
                            height: 20,
                            width: '85%',
                            borderRadius: 5,
                            alignSelf: 'center',
                            justifyContent: 'center',
                            overflow: 'hidden',
                          }}>
                          <CustomShimmer
                            height={20}
                            width={85}
                            style={{marginTop: 5}}
                          />
                        </View>
                      </View>
                    ))}
                  </View>
                );
              }}
              columnWrapperStyle={{
                marginHorizontal: 16,
              }}
              renderItem={({item, index}) => (
                <View
                  style={{
                    width: '25%',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      if (item.module_type == 'independent') {
                        Global.areaId == undefined
                          ? props.navigation.navigate('UnavailableService')
                          : props.navigation.navigate('SingleServiceScreen', {
                              slug: item.slug,
                              isPackage: item.package,
                              bgImage: `${IMAGE_BASE_URL}${item.backgroundImage}`,
                              catName: item.name,
                            });
                      } else {
                        props.navigation.navigate(item.linked_url_app);
                      }
                    }}
                    style={{
                      backgroundColor: '#F1F1F1',
                      height: 80,
                      width: '85%',
                      borderRadius: 12,
                      alignSelf: 'center',
                      justifyContent: 'center',
                      overflow: 'hidden',
                    }}>
                    <DynamicImage
                      uri={`${IMAGE_BASE_URL}${item.image}`}
                      imgSize={89}
                      svgSize={40}
                    />
                  </TouchableOpacity>
                  <Text
                    numberOfLines={2}
                    style={{
                      flex: 1,
                      marginTop: 5,
                      fontFamily: designeSheet.QuicksandBold,
                      fontSize: 12,
                      color: '#000000',
                      textAlign: 'center',
                    }}>
                    {item.name}
                  </Text>
                </View>
              )}
            />
          </View>

          <MostBooked heading={'Most booked services'} reverse={true} />
          <Offers />
          <MostBooked heading={'Most booked products'} reverse={false} />
          <CustomerLovesUs />
        </Animated.ScrollView>
      </View>
      <Loader isActive={isLoading} />

      {cartData.length == 0 ? null : (
        <StickyCartView cartData={cartData} props={props} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  addressView: {
    flexDirection: 'row',
    alignItems: 'center',
    fontSize: 14,
    fontFamily: designeSheet.QuicksandBold,
    color: 'black',
    marginHorizontal: 20,
  },
  addressTextView: {
    flexDirection: 'row',
    marginTop: 10,
    alignItems: 'center',
  },
  addressMainText: {
    marginLeft: 5,
    color: '#000000',
    fontFamily: designeSheet.QuicksandBold,
    fontSize: 14,
  },
  addressText: {
    marginLeft: 5,
    color: '#000000',
    fontSize: 13,
    fontFamily: designeSheet.QuicksandMedium,
  },
});

export default HomeScreen;
