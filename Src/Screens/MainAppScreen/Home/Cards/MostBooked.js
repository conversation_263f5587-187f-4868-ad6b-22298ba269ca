import {
  Dimensions,
  FlatList,
  Image,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {API_URL, IMAGE_BASE_URL} from '../../../../utils/urls';
import designeSheet from '../../../../Designe/designeSheet';

const MostBooked = ({heading, reverse}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [mostBooked, setMostBooked] = useState([]);

  useEffect(() => {
    getMostBooked();
  }, []);

  function getMostBooked() {
    setIsLoading(true);

    fetch(API_URL.mostBooked, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    })
      .then(response =>
        response.json().then(data => ({status: response.status, data})),
      )
      .then(result => {
        console.log('match Location response', result.data);
        if (result.status === 200) {
          setMostBooked(result.data.data);
        }
      })
      .catch(error => {
        console.error('ERROR', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }
  return (
    <View style={{backgroundColor: '#FFF7FB'}}>
      <Text
        style={{
          color: '#000000',
          fontFamily: designeSheet.QuicksandSemiBold,
          fontSize: 18,
          marginLeft: 15,
        }}>
        {heading}
      </Text>
      <FlatList
        data={mostBooked}
        horizontal
        pagingEnabled
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => (
          <View
            style={{
              paddingVertical: 11,
              paddingHorizontal: 9,
              width: Dimensions.get('screen').width / 3,
            }}>
            <Image
              style={{height: 100, borderRadius: 8}}
              source={{uri: `${IMAGE_BASE_URL}${item.product.image}`}}
            />
            <Text
              style={{
                fontSize: 13,
                fontFamily: designeSheet.QuicksandBold,
                color: '#000000',
              }}>
              {item.product.name}
            </Text>
            {/* <View style={{flex: 1}} /> */}
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
              <Image
                style={{height: 12, width: 12}}
                source={{
                  uri: 'https://cdn-icons-png.flaticon.com/128/1828/1828884.png',
                }}
              />
              <Text
                style={{
                  fontSize: 11,
                  fontFamily: designeSheet.QuicksandBold,
                  color: '#000000',
                }}>
                {'4.8 (79 Reviews)'}
              </Text>
            </View>
          </View>
        )}
      />
    </View>
  );
};

export default MostBooked;

const styles = StyleSheet.create({});
