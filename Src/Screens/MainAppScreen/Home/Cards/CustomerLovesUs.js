import {FlatList, StyleSheet, Text, View, Image} from 'react-native';
import React from 'react';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import designeSheet from '../../../../Designe/designeSheet';

const CustomerLovesUs = () => {
  const stories = [
    {
      id: 1,
      title: 'Selfcare Ritual',
      image: ImagePath.hv,
      name: '<PERSON><PERSON>',
      role: 'Student',
    },
    {
      id: 2,
      title: 'Packages',
      image: ImagePath.hv1,
      name: '<PERSON><PERSON>',
      role: 'Student',
    },
    {
      id: 3,
      title: 'Selfcare Ritual',
      image: ImagePath.hv,
      name: '<PERSON><PERSON>',
      role: 'Student',
    },
    {
      id: 4,
      title: 'Packages',
      image: ImagePath.hv1,
      name: '<PERSON><PERSON>',
      role: 'Student',
    },
    {
      id: 5,
      title: 'Selfcare Ritual',
      image: ImagePath.hv,
      name: '<PERSON><PERSON>',
      role: 'Student',
    },
    {
      id: 6,
      title: 'Packages',
      image: ImagePath.hv1,
      name: '<PERSON><PERSON>',
      role: 'Student',
    },
  ];
  return (
    <View style={{flex: 1}}>
      <Text style={styles.storiesTitle}>Customers love us</Text>
      <FlatList
        data={stories}
        keyExtractor={item => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{paddingHorizontal: 10, gap: 19}}
        renderItem={({item}) => (
          <View
            style={{
              alignItems: 'center',
            }}>
            <Image source={item.image} style={styles.storyImage} />
            <View style={styles.playIconOverlay}>
              <Text style={{color: '#fff', fontSize: 24}}>▶</Text>
            </View>
            <Text style={styles.storyName}>{item.name}</Text>
            <Text style={styles.storyRole}>{item.role}</Text>
          </View>
        )}
      />
    </View>
  );
};

export default CustomerLovesUs;

const styles = StyleSheet.create({
  storyName: {
    fontSize: 14,
    fontFamily: designeSheet.QuicksandSemiBold,
    color: '#000',
    marginTop: 6,
  },
  storyRole: {
    fontSize: 12,
    color: '#666',
  },
  storiesTitle: {
    color: '#000',
    marginHorizontal: 15,
    marginTop: 15,
    marginBottom: 8,

    fontFamily: designeSheet.QuicksandSemiBold,
    fontSize: 18,
  },
  storyCard: {
    width: 120,
    marginRight: 12,
    alignItems: 'center',
  },
  storyImage: {
    width: 120,
    height: 150,
    borderRadius: 8,
  },
  playIconOverlay: {
    position: 'absolute',
    top: 55,
    left: 50,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
