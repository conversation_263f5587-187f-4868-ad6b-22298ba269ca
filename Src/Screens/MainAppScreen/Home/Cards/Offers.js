import {
  Dimensions,
  FlatList,
  Image,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import designeSheet from '../../../../Designe/designeSheet';
import LinearGradient from 'react-native-linear-gradient';
import ImagePath from '../../../../Assets/ImagePath/ImagePath';
import {API_URL, IMAGE_BASE_URL} from '../../../../utils/urls';
import CustomShimmer from '../../../../components/CustomShimmer';

const Offers = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [offers, setOffers] = useState([]);

  useEffect(() => {
    getHomeOffers();
  }, []);

  function getHomeOffers() {
    setIsLoading(true);

    fetch(API_URL.offers, {
      method: 'GET',
      headers: {
        'Content-type': 'application/json',
      },
    })
      .then(response =>
        response.json().then(data => ({status: response.status, data})),
      )
      .then(result => {
        console.log('match Location response', result.data);
        if (result.status === 200) {
          setOffers(result.data.data);
        }
      })
      .catch(error => {
        console.error('ERROR', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  return (
    <View style={{marginVertical: 10}}>
      <FlatList
        data={offers}
        horizontal
        pagingEnabled
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => (
          <View
            style={{
              width: Dimensions.get('screen').width,
              justifyContent: 'center',
            }}>
            <LinearGradient
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              locations={[0.0, 1.0]}
              colors={['#F1D8FF', '#E98AA3']}
              useViewFrame={true}
              style={{marginHorizontal: 15, borderRadius: 12}}>
              <View
                style={{
                  paddingVertical: 11,
                  paddingHorizontal: 9,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <View style={{flex: 1}}>
                  <Text
                    numberOfLines={3}
                    style={{
                      fontSize: 18,
                      fontFamily: designeSheet.QuicksandBold,
                      color: '#990057',
                    }}>
                    {item.title}
                  </Text>
                  <Text
                    style={{
                      marginTop: 10,
                      fontSize: 14,
                      fontFamily: designeSheet.QuicksandMedium,
                      color: '#990057',
                    }}>
                    {item.category.name}
                  </Text>
                  <Text
                    style={{
                      marginTop: 10,
                      color: '#ffffff',
                      backgroundColor: '#990057',
                      paddingVertical: 4.64,
                      paddingHorizontal: 10.45,
                      borderRadius: 4.64,
                      alignSelf: 'flex-start',
                    }}>
                    Book Now
                  </Text>
                </View>
                <Image
                  style={{height: 131, flex: 0.5, borderRadius: 8}}
                  source={{uri: `${IMAGE_BASE_URL}${item.image}`}}
                />
              </View>
            </LinearGradient>
          </View>
        )}
      />
      <View
        style={{
          borderRadius: 12,
          overflow: 'hidden',
          backgroundColor: 'red',
          alignSelf: 'center',
        }}>
        {isLoading ? (
          <CustomShimmer
            height={150}
            width={Dimensions.get('screen').width - 30}
          />
        ) : null}
      </View>
    </View>
  );
};

export default Offers;

const styles = StyleSheet.create({});
