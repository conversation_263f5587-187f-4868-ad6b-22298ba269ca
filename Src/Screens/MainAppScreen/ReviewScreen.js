import {
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import ImagePath from '../../Assets/ImagePath/ImagePath';
import designeSheet from '../../Designe/designeSheet';

const ReviewScreen = (props) => {
  const [reviewText, setReviewText] = useState('');
  const [reviewPopup, setReviewPopup] = useState(false);
  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp title={'Review'} onpressback={()=> {
props.navigation.goBack();
      }} />
      <View style={{flex: 1}}>
        <View
          style={{
            flexDirection: 'row',
            marginHorizontal: 20,
            marginTop: 20,
            alignItems: 'center',
            gap: 3,
            backgroundColor: '#00000008',
            borderRadius: 12,
          }}>
          <Image
            source={ImagePath.reviewimg}
            style={{
              height: 48,
              width: 48,
              marginHorizontal: 10,
              marginVertical: 10,
            }}
          />
          <View style={{marginHorizontal: 5}}>
            <Text
              style={{
                fontSize: 16,
                fontFamily: designeSheet.QuicksandSemiBold,
                color: '#000000',
              }}>
              {'Salon At Home'}
            </Text>
            <View style={{
              flexDirection: 'row', 
              alignItems: 'center'
              }}>
              <Image 
              source={ImagePath.star} 
              style={{
                height: 14, 
                width: 14
                }} />
              <Text
                style={{
                  fontSize: 12,
                  fontFamily: designeSheet.QuicksandRegular,
                  color: '#6B7280',
                  marginHorizontal: 5,
                }}>
                {'4.0'}
              </Text>
            </View>
          </View>
        </View>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandMedium,
            color: '#2F3131',
            marginHorizontal: 20,
            marginTop: 20,
          }}>
          {'Overall rating'}
        </Text>
        <View
          style={{
            flexDirection: 'row', 
            marginTop: 10, 
            marginHorizontal: 20
            }}>
          {[0, 0, 0, 0, 0].map((screen, index) => (
            <View key={index}>
              <Image 
              source={ImagePath.star1} 
              style={{
                height: 28, 
                width: 28
                }} />
            </View>
          ))}
        </View>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandMedium,
            color: '#2F3131',
            marginHorizontal: 20,
            marginTop: 20,
          }}>
          {'Write a review.'}
        </Text>
        <TextInput
          placeholder="   How was your experience? "
          style={{
            borderWidth: 1,
            borderColor: '#CCCCDCCC',
            borderRadius: 12,
            marginHorizontal: 20,
            marginTop: 10,
            fontSize: 14,
            fontFamily: designeSheet.QuicksandRegular,
            height: 186,
            width: 335,
          }}
          placeholderTextColor={'#5B5F5F'}
          value={reviewText}
          onChangeText={text => setReviewText(text)}
        />
      </View>
      <TouchableOpacity
        onPress={() => {
          setReviewPopup(true);
          setTimeout(() => {
            setReviewPopup(false);
          }, 2000);
        }}
        style={{
          backgroundColor: '#000000',
          borderRadius: 8,
          marginBottom: 20,
          marginHorizontal: 20,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: designeSheet.QuicksandSemiBold,
            color: '#FFFFFF',
            marginVertical: 12,
          }}>
          {'Leave a Review'}
        </Text>
      </TouchableOpacity>
      {reviewPopup ? (
        <View
          style={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              backgroundColor: '#fff',
              borderRadius: 16,
              padding: 50,
              width: '90%',
              alignSelf: 'center',
              elevation: 6,
              shadowColor: '#000',
              shadowOpacity: 0.15,
              shadowRadius: 10,
              shadowOffset: {width: 0, height: 4},
            }}>
            <Text
              style={{
                fontSize: 20,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#000000',
                textAlign: 'center',
              }}>
              {'Thank you for your feedback!'}
            </Text>
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#6C6C6C',
                textAlign: 'center',
                marginTop: 5,
              }}>
              {
                'Your review has been submitted successfully and will help others make informed decisions.'
              }
            </Text>
          </View>
        </View>
      ) : null}
    </View>
  );
};

export default ReviewScreen;

const styles = StyleSheet.create({});
