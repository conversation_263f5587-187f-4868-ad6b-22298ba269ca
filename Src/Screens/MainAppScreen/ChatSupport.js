import {
  Image,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import HeaderComp from '../../components/HeaderComp';
import designeSheet from '../../Designe/designeSheet';
import ImagePath from '../../Assets/ImagePath/ImagePath';

const ChatSupport = props => {
  const [hint, setHint] = useState([
    {
      text: 'Technical Help',
    },
    {
      text: 'Services',
    },
    {
      text: 'Pricing',
    },
    {
      text: 'Booking an Appointment',
    },
    {
      text: 'Refunds',
    },
    {
      text: 'Hygiene',
    },
    {
      text: 'Promotions & Offers',
    },
    {
      text: 'Pricing',
    },
  ]);
  const [message, setMessage] = useState('');

  return (
    <View style={{flex: 1, backgroundColor: '#FFF7FB'}}>
      <HeaderComp
        title={'Chat & Support'}
        onpressback={() => {
          props.navigation.goBack();
        }}
      />
      <View style={{flex: 1}}>
        <Text
          style={{
            fontSize: 14,
            color: '#6C6C6C',
            fontFamily: designeSheet.QuicksandMedium,
            alignSelf: 'center',
            marginTop: 20,
          }}>
          {'Today 12:00Am'}
        </Text>
        <View
          style={{
            borderWidth: 1,
            borderColor: '#CCCCCCCC',
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            borderBottomRightRadius: 16,
            marginHorizontal: 20,
            marginTop: 5,
          }}>
          <Text
            style={{
              fontSize: 14,
              fontFamily: designeSheet.QuicksandRegular,
              color: '#6C6C6C',
              marginTop: 12,
              marginHorizontal: 16,
            }}>
            {
              'Hey there! 👋 Welcome to Madam G. We’re so excited to have you here. Whether you’re looking to book your next appointment, have questions about our services, or just want to chat, our team is here to help. Let’s get you feeling fabulous! 💛'
            }
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 8,
              marginHorizontal: 16,
            }}>
            <Text
              style={{
                fontSize: 12,
                fontFamily: designeSheet.QuicksandMedium,
                color: '#6C6C6C',
              }}>
              {'11:22'}
            </Text>
            <Image
              source={ImagePath.doubletick}
              style={{
                height: 14,
                width: 14,
                marginHorizontal: 8,
              }}
            />
          </View>
        </View>
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: 10,
            marginTop: 25,
            marginHorizontal: 20,
          }}>
          {hint.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={{backgroundColor: '#000000', borderRadius: 4}}>
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: designeSheet.QuicksandSemiBold,
                  color: '#FCFCFC',
                  marginHorizontal: 16,
                  marginVertical: 4,
                }}>
                {item.text}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            borderWidth: 0.5,
            borderColor: '#CCCCCCCC',

            marginHorizontal: 5,
            marginTop: 10,
            marginBottom: Platform.OS == 'ios' ? 20 : 10,
            borderRadius: 8,
          }}>
          <TextInput
            placeholder="Type a message ..."
            style={{
              flex: 1,
              borderRadius: 12,
              marginHorizontal: 20,
              marginTop: 10,
              fontSize: 14,
              fontFamily: designeSheet.QuicksandRegular,
              padding: 0,
              marginVertical: Platform.OS == 'ios' ? 20 : 0,
            }}
            placeholderTextColor={'#5B5F5F'}
            value={message}
            onChangeText={text => setMessage(text)}
          />
          <Image
            source={ImagePath.camera}
            style={{
              height: 24,
              width: 24,
            }}
          />
          <Image
            source={ImagePath.gallery}
            style={{
              height: 24,
              width: 24,
              marginHorizontal: 10,
            }}
          />
        </View>
        <Image
          source={ImagePath.sendmsg}
          style={{
            height: 50,
            width: 50,
            marginHorizontal: 5,
          }}
        />
      </View>
    </View>
  );
};

export default ChatSupport;

const styles = StyleSheet.create({});
