export interface UserModal {
  name: string;
  email: string;
  mobile: string;
  image: string;
  gender: string;
  role: string;
  perMinutCharges: number;
  isProfileComplete: boolean;
  verified: boolean;
  status: string;
  _id: string;
  startUpAt: string;
  documents: any[];
  created: string;
  updated: string;
  __v: number;
  refreshToken: string;
  id: string;
}

export const userModel = (data: any): UserModal => ({
  name: data.name,
  email: data.email,
  mobile: data.mobile,
  image: data.image,
  gender: data.gender,
  role: data.role,
  perMinutCharges: data.perMinutCharges,
  isProfileComplete: data.isProfileComplete,
  verified: data.verified,
  status: data.status,
  _id: data._id,
  startUpAt: data.startUpAt,
  documents: data.documents || [],
  created: data.created,
  updated: data.updated,
  __v: data.__v,
  refreshToken: data.refreshToken,
  id: data.id,
});

export interface SessionModal {
  sessionId: string;
  channelName: string;
  token: string;
  role: string;
}

export const sessionModel = (data: any): SessionModal => ({
  sessionId: data.sessionId,
  channelName: data.channelName,
  token: data.token,
  role: data.role,
});
