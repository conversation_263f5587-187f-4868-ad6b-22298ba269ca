{"name": "MadamG", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/geolocation": "^3.4.0", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@reduxjs/toolkit": "^2.8.2", "i": "^0.3.7", "moment": "^2.30.1", "npm": "^10.9.3", "react": "19.1.0", "react-native": "0.80.0", "react-native-agora": "^4.5.3", "react-native-calendars": "^1.1312.1", "react-native-confirmation-code-field": "^8.0.1", "react-native-gesture-handler": "^2.27.2", "react-native-image-crop-picker": "^0.50.1", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.25.0", "react-native-reanimated": "^3.19.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.13.1", "react-native-shimmer-placeholder": "^2.0.9", "react-native-svg": "^15.12.0", "react-native-vision-camera": "^4.7.1", "react-native-vision-camera-face-detector": "^1.8.8", "react-native-worklets-core": "^1.6.2", "react-redux": "^9.2.0", "stacktrace-parser": "^0.1.11"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.3", "@react-native/eslint-config": "0.79.3", "@react-native/metro-config": "0.79.3", "@react-native/typescript-config": "0.79.3", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}